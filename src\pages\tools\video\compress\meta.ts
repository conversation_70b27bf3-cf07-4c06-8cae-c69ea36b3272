import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Compress',
  path: 'compress',
  icon: 'mdi:tools',
  description: 'Compress tool for video operations. Perform compress operations efficiently.',
  shortDescription: 'Compress tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
