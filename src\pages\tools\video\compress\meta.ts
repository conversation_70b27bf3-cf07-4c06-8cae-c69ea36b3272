import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Tool',
  path: 'compress',
  icon: 'mdi:tools',
  description: 'Compress videos by scaling them to different resolutions like 240p, 480p, 720p, etc. This tool helps reduce file size while maintaining acceptable quality. Supports common video formats like MP4, WebM, and OGG.',
  shortDescription: 'Compress videos by scaling to different resolutions',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
