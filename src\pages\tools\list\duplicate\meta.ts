import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Duplicate',
  path: 'duplicate',
  icon: 'mdi:tools',
  description: 'Duplicate tool for list operations. Perform duplicate operations efficiently.',
  shortDescription: 'Duplicate tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
