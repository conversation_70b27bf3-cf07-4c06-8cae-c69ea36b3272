import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Tool',
  path: 'duplicate',
  icon: 'mdi:tools',
  description: 'A tool to duplicate each item in a list a specified number of times. Perfect for creating repeated patterns, test data, or expanding datasets.',
  shortDescription: 'Repeat items in a list multiple times.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
