import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Tool',
  path: 'flip',
  icon: 'mdi:tools',
  description: 'This online utility allows you to flip videos horizontally or vertically. You can preview the flipped video before processing. Supports common video formats like MP4, WebM, and OGG.',
  shortDescription: 'Flip videos horizontally or vertically',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
