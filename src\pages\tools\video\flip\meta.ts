import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Flip',
  path: 'flip',
  icon: 'mdi:tools',
  description: 'Flip tool for video operations. Perform flip operations efficiently.',
  shortDescription: 'Flip tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
