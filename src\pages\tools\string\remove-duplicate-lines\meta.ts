import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Remove Duplicate Lines',
  path: 'remove-duplicate-lines',
  icon: 'mdi:tools',
  description: 'Remove Duplicate Lines tool for string operations. Perform remove duplicate lines operations efficiently.',
  shortDescription: 'Remove Duplicate Lines tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
