import { useTranslation } from 'react-i18next';
import { ToolCategory } from '../tools/defineTool';

export interface ToolTranslation {
  name: string;
  description: string;
  shortDescription: string;
}

export const useToolTranslation = (category: ToolCategory, toolPath: string): ToolTranslation => {
  const { t, i18n } = useTranslation(['tools', 'common']);
  
  // Create the translation key path
  const basePath = `tools:${category}.${toolPath}`;
  
  // Get translations with fallbacks
  const name = t(`${basePath}.name`, { defaultValue: '' });
  const description = t(`${basePath}.description`, { defaultValue: '' });
  const shortDescription = t(`${basePath}.shortDescription`, { defaultValue: '' });
  
  return {
    name,
    description,
    shortDescription
  };
};

export const getToolTranslation = (
  category: ToolCategory, 
  toolPath: string, 
  language: string = 'en'
): ToolTranslation => {
  // This is a utility function for getting translations without hooks
  // Useful for static contexts or server-side rendering
  
  try {
    // Import the translation files dynamically based on language
    const translations = language === 'ru' 
      ? require('../locales/ru/tools.json')
      : require('../locales/en/tools.json');
    
    const toolTranslations = translations[category]?.[toolPath];
    
    if (toolTranslations) {
      return {
        name: toolTranslations.name || '',
        description: toolTranslations.description || '',
        shortDescription: toolTranslations.shortDescription || ''
      };
    }
  } catch (error) {
    console.warn(`Translation not found for ${category}.${toolPath} in ${language}`);
  }
  
  // Return empty strings as fallback
  return {
    name: '',
    description: '',
    shortDescription: ''
  };
};
