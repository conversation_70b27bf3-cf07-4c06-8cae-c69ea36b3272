import ToolLayout from '../components/ToolLayout';
import React, { JSXElementConstructor, LazyExoticComponent } from 'react';
import { IconifyIcon } from '@iconify/react';
import { useToolTranslation } from '../hooks/useToolTranslation';
import { useTranslation } from 'react-i18next';

export interface ToolMeta {
  path: string;
  component: LazyExoticComponent<JSXElementConstructor<ToolComponentProps>>;
  keywords: string[];
  icon: IconifyIcon | string;
  name: string;
  description: string;
  shortDescription: string;
  longDescription?: string;
}

export type ToolCategory =
  | 'string'
  | 'png'
  | 'number'
  | 'gif'
  | 'video'
  | 'list'
  | 'json'
  | 'time'
  | 'csv'
  | 'pdf'
  | 'image-generic';

export interface DefinedTool {
  type: ToolCategory;
  path: string;
  name: string;
  description: string;
  shortDescription: string;
  icon: IconifyIcon | string;
  keywords: string[];
  component: () => JSX.Element;
}

export interface ToolComponentProps {
  title: string;
  longDescription?: string;
}

// Component wrapper that handles localization
const LocalizedToolWrapper: React.FC<{
  category: ToolCategory;
  toolPath: string;
  icon: IconifyIcon | string;
  Component: LazyExoticComponent<JSXElementConstructor<ToolComponentProps>>;
  fallbackName: string;
  fallbackDescription: string;
  longDescription?: string;
}> = ({ category, toolPath, icon, Component, fallbackName, fallbackDescription, longDescription }) => {
  const toolTranslation = useToolTranslation(category, toolPath);
  const { t } = useTranslation();

  // Use translation if available, otherwise fallback to original
  const displayName = toolTranslation.name || fallbackName;
  const displayDescription = toolTranslation.description || fallbackDescription;

  return (
    <ToolLayout
      title={displayName}
      description={displayDescription}
      icon={icon}
      type={category}
    >
      <Component title={displayName} longDescription={longDescription} />
    </ToolLayout>
  );
};

export const defineTool = (
  basePath: ToolCategory,
  options: ToolMeta
): DefinedTool => {
  const {
    icon,
    path,
    name,
    description,
    keywords,
    component,
    shortDescription,
    longDescription
  } = options;

  return {
    type: basePath,
    path: `${basePath}/${path}`,
    name,
    icon,
    description,
    shortDescription,
    keywords,
    component: () => {
      return (
        <LocalizedToolWrapper
          category={basePath}
          toolPath={path}
          icon={icon}
          Component={component}
          fallbackName={name}
          fallbackDescription={description}
          longDescription={longDescription}
        />
      );
    }
  };
};
