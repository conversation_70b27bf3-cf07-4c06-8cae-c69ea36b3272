#!/usr/bin/env node

/**
 * Fix Meta Syntax Script
 * 
 * This script fixes all syntax errors in meta.ts files by:
 * 1. Fixing unterminated string literals
 * 2. Escaping quotes properly
 * 3. Fixing multiline strings
 * 4. Restoring proper syntax
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class MetaSyntaxFixer {
  constructor() {
    this.fixedFiles = 0;
    this.translations = {};
    this.loadTranslations();
  }

  loadTranslations() {
    try {
      this.translations = JSON.parse(fs.readFileSync('src/locales/en/tools.json', 'utf8'));
    } catch (error) {
      console.log('⚠️  Could not load translations');
    }
  }

  getToolPath(filePath) {
    const match = filePath.match(/src[\/\\]pages[\/\\]tools[\/\\](.+)[\/\\]meta\.ts$/);
    return match ? match[1].replace(/\\/g, '/') : null;
  }

  getTranslationKey(toolPath) {
    return toolPath.replace(/\//g, '.');
  }

  getNestedProperty(obj, path) {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return current;
  }

  fixMetaFile(filePath) {
    console.log(`Fixing meta syntax: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    const toolPath = this.getToolPath(filePath);
    if (!toolPath) return;
    
    const translationKey = this.getTranslationKey(toolPath);
    
    // Get original descriptions from translations
    const description = this.getNestedProperty(this.translations, translationKey + '.description');
    const shortDescription = this.getNestedProperty(this.translations, translationKey + '.shortDescription');
    
    if (description || shortDescription) {
      // Completely rewrite the meta file with proper syntax
      const toolName = this.getNestedProperty(this.translations, translationKey + '.name') || 'Tool';
      const keywords = this.extractKeywords(content);
      
      const newContent = this.generateMetaFile(toolName, description, shortDescription, keywords, toolPath);
      
      if (newContent !== content) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        this.fixedFiles++;
        console.log(`✅ Fixed: ${filePath}`);
        modified = true;
      }
    } else {
      // Try to fix syntax errors manually
      const fixedContent = this.fixSyntaxErrors(content);
      if (fixedContent !== content) {
        fs.writeFileSync(filePath, fixedContent, 'utf8');
        this.fixedFiles++;
        console.log(`✅ Fixed: ${filePath}`);
        modified = true;
      }
    }
    
    if (!modified) {
      console.log(`⏭️  No fixes needed: ${filePath}`);
    }
  }

  extractKeywords(content) {
    const keywordsMatch = content.match(/keywords:\s*\[([^\]]+)\]/);
    if (keywordsMatch) {
      try {
        return JSON.parse('[' + keywordsMatch[1] + ']');
      } catch (e) {
        return ['tool'];
      }
    }
    return ['tool'];
  }

  generateMetaFile(name, description, shortDescription, keywords, toolPath) {
    const category = toolPath.split('/')[0];
    const subCategory = toolPath.split('/')[1] || '';
    
    let categoryName = category;
    if (category === 'image' && subCategory) {
      categoryName = `image-${subCategory}`;
    }
    
    const escapedDescription = description ? description.replace(/'/g, "\\'") : 'Tool description';
    const escapedShortDescription = shortDescription ? shortDescription.replace(/'/g, "\\'") : 'Tool';
    const keywordsStr = JSON.stringify(keywords);
    
    return `import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('${categoryName}', {
  name: '${name}',
  path: '${toolPath.split('/').pop()}',
  icon: 'mdi:tools',
  description: '${escapedDescription}',
  shortDescription: '${escapedShortDescription}',
  keywords: ${keywordsStr},
  component: lazy(() => import('./index'))
});
`;
  }

  fixSyntaxErrors(content) {
    let fixed = content;
    
    // Fix unterminated string literals
    fixed = fixed.replace(/description:\s*'([^']*)"[^']*$/gm, (match, p1) => {
      return `description: '${p1.replace(/'/g, "\\'")}',`;
    });
    
    // Fix multiline strings that are broken
    fixed = fixed.replace(/description:\s*'([^']*)\n[^']*$/gm, (match, p1) => {
      return `description: '${p1.replace(/'/g, "\\'")}',`;
    });
    
    // Fix shortDescription syntax errors
    fixed = fixed.replace(/shortDescription:\s*'([^']*)"[^']*$/gm, (match, p1) => {
      return `shortDescription: '${p1.replace(/'/g, "\\'")}',`;
    });
    
    // Remove any stray characters at the end of lines
    fixed = fixed.replace(/[^,\s]\s*$/gm, '');
    
    // Ensure proper closing
    if (!fixed.includes('});')) {
      fixed = fixed.replace(/\s*$/, '\n});');
    }
    
    return fixed;
  }

  fixAllMetaFiles() {
    console.log('🔧 Starting meta syntax fixes...\n');
    
    const files = glob.sync('src/pages/tools/**/meta.ts', {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    console.log(`Found ${files.length} meta files to fix\n`);
    
    files.forEach(file => {
      this.fixMetaFile(file);
    });
    
    console.log('\n📊 SUMMARY:');
    console.log(`Files fixed: ${this.fixedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const fixer = new MetaSyntaxFixer();
  fixer.fixAllMetaFiles();
  console.log('\n✅ Meta syntax fixes completed!');
}

module.exports = MetaSyntaxFixer;
