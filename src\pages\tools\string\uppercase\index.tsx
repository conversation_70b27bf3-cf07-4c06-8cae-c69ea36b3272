import React, { useState } from 'react';
import ToolTextInput from '@components/input/ToolTextInput';
import ToolTextResult from '@components/result/ToolTextResult';
import { UppercaseInput } from './service';
import { CardExampleType } from '@components/examples/ToolExamples';
import { ToolComponentProps } from '@tools/defineTool';
import ToolContent from '@components/ToolContent';
import { useTranslation } from 'react-i18next';

const initialValues = {};

export default function Uppercase({ title }: ToolComponentProps) {
  const { t } = useTranslation();

  const exampleCards: CardExampleType<typeof initialValues>[] = [
    {
      title: t('tools:string.uppercase.example1.title'),
      description: t('tools:string.uppercase.example1.description'),
      sampleText: 'The quick brown fox jumps over the lazy dog.',
      sampleResult: 'THE QUICK BROWN FOX JUMPS OVER THE LAZY DOG.',
      sampleOptions: {}
    },
    {
      title: t('tools:string.uppercase.example2.title'),
      description: t('tools:string.uppercase.example2.description'),
      sampleText: 'function example() { return "hello world"; }',
      sampleResult: 'FUNCTION EXAMPLE() { RETURN "HELLO WORLD"; }',
      sampleOptions: {}
    },
    {
      title: t('tools:string.uppercase.example3.title'),
      description: t('tools:string.uppercase.example3.description'),
      sampleText: 'ThIs Is MiXeD CaSe TeXt!',
      sampleResult: 'THIS IS MIXED CASE TEXT!',
      sampleOptions: {}
    }
  ];

  const [input, setInput] = useState<string>('');
  const [result, setResult] = useState<string>('');

  const computeExternal = (
    _optionsValues: typeof initialValues,
    input: string
  ) => {
    setResult(UppercaseInput(input));
  };

  return (
    <ToolContent
      title={title}
      initialValues={initialValues}
      getGroups={null}
      compute={computeExternal}
      input={input}
      setInput={setInput}
      inputComponent={<ToolTextInput value={input} onChange={setInput} />}
      resultComponent={
        <ToolTextResult title={t('common.uppercaseText')} value={result} />
      }
      exampleCards={exampleCards}
    />
  );
}
