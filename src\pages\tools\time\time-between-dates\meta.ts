import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Time Between Dates',
  path: 'time-between-dates',
  icon: 'mdi:tools',
  description: 'Time Between Dates tool for time operations. Perform time between dates operations efficiently.',
  shortDescription: 'Time Between Dates tool for time operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
