import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Tsv To Json',
  path: 'tsv-to-json',
  icon: 'mdi:tools',
  description: 'Tsv To Json tool for json operations. Perform tsv to json operations efficiently.',
  shortDescription: 'Tsv To Json tool for json operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
