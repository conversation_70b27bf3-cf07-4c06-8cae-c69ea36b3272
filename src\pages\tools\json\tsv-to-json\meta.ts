import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Tool',
  path: 'tsv-to-json',
  icon: 'mdi:tools',
  description: 'Convert TSV files to JSON format with customizable options for delimiters, quotes, and output formatting. Support for headers, comments, and dynamic type conversion.',
  shortDescription: 'Convert TSV data to JSON format.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
