import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Tool',
  path: 'compress-png',
  icon: 'mdi:tools',
  description: 'This is a program that compresses PNG pictures. As soon as you paste your PNG picture in the input area, the program will compress it and show the result in the output area. In the options, you can adjust the compression level, as well as find the old and new picture file sizes.',
  shortDescription: 'Quickly compress a PNG',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
