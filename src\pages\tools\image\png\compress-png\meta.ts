import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Compress Png',
  path: 'compress-png',
  icon: 'mdi:tools',
  description: 'Compress Png tool for image operations. Perform compress png operations efficiently.',
  shortDescription: 'Compress Png tool for image operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
