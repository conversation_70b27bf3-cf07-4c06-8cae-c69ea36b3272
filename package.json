{"name": "omni-tools", "description": "This project offers a variety of online tools to help with everyday tasks, \nall available for free and open for community contributions", "version": "0.1.0", "private": true, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/iib0011"}, "bugs": {"url": "https://github.com/iib0011/omni-tools/issues", "email": "<EMAIL>"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview", "test": "vitest", "test:e2e": "playwright test", "test:ui": "vitest --ui", "script:create:tool": "node scripts/create-tool.mjs", "lint": "eslint src --max-warnings=0 --fix", "typecheck": "tsc --project tsconfig.json --noEmit", "prepare": "husky install"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@imgly/background-removal": "^1.6.0", "@jimp/types": "^1.6.0", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@playwright/test": "^1.45.0", "@types/ffmpeg": "^1.0.7", "@types/js-quantities": "^1.6.6", "@types/lodash": "^4.17.5", "@types/morsee": "^1.0.2", "@types/omggif": "^1.0.5", "browser-image-compression": "^2.0.2", "buffer": "^6.0.3", "color": "^4.2.3", "dayjs": "^1.11.13", "formik": "^2.4.6", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "jimp": "^0.22.12", "js-quantities": "^1.8.0", "jszip": "^3.10.1", "lint-staged": "^15.4.3", "lodash": "^4.17.21", "mime": "^4.0.6", "morsee": "^1.0.9", "nerdamer-prime": "^1.2.4", "notistack": "^3.0.1", "omggif": "^1.0.10", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.2.133", "playwright": "^1.45.0", "qrcode": "^1.5.4", "rc-slider": "^11.1.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-i18next": "^15.6.0", "react-image-crop": "^11.0.7", "react-router-dom": "^6.23.1", "tesseract.js": "^6.0.0", "type-fest": "^4.35.0", "use-deep-compare-effect": "^1.8.1", "yup": "^1.4.0"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@iconify/react": "^5.2.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^14.3.1", "@types/color": "^3.0.6", "@types/color-rgba": "^2.1.2", "@types/node": "^20.12.12", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react-swc": "^3.7.0", "@vitest/ui": "^1.6.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-tailwindcss": "^3.17.0", "happy-dom": "^12.10.3", "husky": "^9.0.11", "postcss": "^8.4.38", "prettier": "3.1.1", "start-server-and-test": "^2.0.4", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.2.11", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.0"}, "lint-staged": {"*.{ts,tsx,js,jsx,json}": "prettier --write"}}