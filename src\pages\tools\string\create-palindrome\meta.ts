import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Create Palindrome',
  path: 'create-palindrome',
  icon: 'mdi:tools',
  description: 'Create Palindrome tool for string operations. Perform create palindrome operations efficiently.',
  shortDescription: 'Create Palindrome tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
