import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Truncate Clock Time',
  path: 'truncate-clock-time',
  icon: 'mdi:tools',
  description: 'With this browser-based application, you can truncate a clock time and drop the minutes and/or seconds components from it. If you drop the seconds, you will be left with hours and minutes. For example, ',
  shortDescription: 'Quickly convert clock time in H:M:S format to seconds.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
