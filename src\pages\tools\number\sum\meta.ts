import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('number', {
  name: 'Sum Numbers',
  path: 'sum',
  icon: 'mdi:tools',
  description: 'Quickly calculate the sum of numbers in your browser. To get your sum, just enter your list of numbers in the input field, adjust the separator between the numbers in the options below, and this utility will add up all these numbers.',
  shortDescription: 'Quickly sum numbers',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
