import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('number', {
  name: 'Sum Numbers',
  path: 'sum',
  icon: 'mdi:tools',
  description: 'Sum Numbers tool for number operations. Perform sum operations efficiently.',
  shortDescription: 'Sum Numbers tool for number operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
