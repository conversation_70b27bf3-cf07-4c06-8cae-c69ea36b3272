import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Replace Text',
  path: 'text-replacer',
  icon: 'mdi:tools',
  description: 'Easily replace specific text in your content with this simple, browser-based tool. Just input your text, set the text you want to replace and the replacement value, and instantly get the updated version.',
  shortDescription: 'Quickly replace text in your content',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
