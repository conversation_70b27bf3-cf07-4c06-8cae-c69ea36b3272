import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Replace Text',
  path: 'text-replacer',
  icon: 'mdi:tools',
  description: 'Replace Text tool for string operations. Perform text replacer operations efficiently.',
  shortDescription: 'Replace Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
