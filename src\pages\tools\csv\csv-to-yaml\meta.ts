import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Tool',
  path: 'csv-to-yaml',
  icon: 'mdi:tools',
  description: 'Just upload your CSV file in the form below and it will automatically get converted to a YAML file. In the tool options, you can specify the field delimiter character, field quote character, and comment character to adapt the tool to custom CSV formats. Additionally, you can select the output YAML format: one that preserves CSV headers or one that excludes CSV headers.',
  shortDescription: 'Quickly convert a CSV file to a YAML file.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
