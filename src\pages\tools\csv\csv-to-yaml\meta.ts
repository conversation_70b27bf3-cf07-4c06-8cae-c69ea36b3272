import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Csv To Yaml',
  path: 'csv-to-yaml',
  icon: 'mdi:tools',
  description: 'Csv To Yaml tool for csv operations. Perform csv to yaml operations efficiently.',
  shortDescription: 'Csv To Yaml tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
