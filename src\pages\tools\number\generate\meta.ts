import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('number', {
  name: 'Generate Numbers',
  path: 'generate',
  icon: 'mdi:tools',
  description: 'Quickly calculate a list of integers in your browser. To get your list, just specify the first integer, change value and total count in the options below, and this utility will generate that many integers',
  shortDescription: 'Quickly calculate a list of integers in your browser',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
