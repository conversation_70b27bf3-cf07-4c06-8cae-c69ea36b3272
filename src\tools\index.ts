import { stringTools } from '../pages/tools/string';
import { imageTools } from '../pages/tools/image';
import { DefinedTool, ToolCategory } from './defineTool';
import { capitalizeFirstLetter } from '../utils/string';
import { numberTools } from '../pages/tools/number';
import { videoTools } from '../pages/tools/video';
import { listTools } from '../pages/tools/list';
import { Entries } from 'type-fest';
import { jsonTools } from '../pages/tools/json';
import { csvTools } from '../pages/tools/csv';
import { timeTools } from '../pages/tools/time';
import { IconifyIcon } from '@iconify/react';
import { pdfTools } from '../pages/tools/pdf';
import { useTranslation } from 'react-i18next';

const toolCategoriesOrder: ToolCategory[] = [
  'image-generic',
  'string',
  'json',
  'pdf',
  'video',
  'list',
  'csv',
  'number',
  'png',
  'time',
  'gif'
];
export const tools: DefinedTool[] = [
  ...imageTools,
  ...stringTools,
  ...jsonTools,
  ...pdfTools,
  ...listTools,
  ...csvTools,
  ...videoTools,
  ...numberTools,
  ...timeTools
];
function getCategoriesConfig(t: any): {
  type: ToolCategory;
  value: string;
  title?: string;
  icon: IconifyIcon | string;
}[] {
  return [
  {
    type: 'string',
    title: t('categories.string.title'),
    icon: 'solar:text-bold-duotone',
    value: t('categories.string.description')
  },
  {
    type: 'png',
    icon: 'ph:file-png-thin',
    value: t('categories.png.description')
  },
  {
    type: 'number',
    icon: 'lsicon:number-filled',
    value: t('categories.number.description')
  },
  {
    type: 'gif',
    icon: 'material-symbols-light:gif-rounded',
    value: t('categories.gif.description')
  },
  {
    type: 'list',
    icon: 'solar:list-bold-duotone',
    value: t('categories.list.description')
  },
  {
    type: 'json',
    icon: 'lets-icons:json-light',
    value: t('categories.json.description')
  },
  {
    type: 'time',
    icon: 'mdi:clock-time-five',
    value: t('categories.time.description')
  },
  {
    type: 'csv',
    icon: 'material-symbols-light:csv-outline',
    value: t('categories.csv.description')
  },
  {
    type: 'video',
    icon: 'lets-icons:video-light',
    value: t('categories.video.description')
  },
  {
    type: 'pdf',
    icon: 'tabler:pdf',
    value: t('categories.pdf.description')
  },
  {
    type: 'time',
    icon: 'fluent-mdl2:date-time',
    value:
      'Tools for working with time and date – draw clocks and calendars, generate time and date sequences, calculate average time, convert between time zones, and much more.'
  },
  {
    type: 'image-generic',
    title: 'Image',
    icon: 'material-symbols-light:image-outline-rounded',
    value:
      'Tools for working with pictures – compress, resize, crop, convert to JPG, rotate, remove background and much more.'
  }
];
// use for changelogs
// console.log(
//   'tools',
//   tools.map(({ name, type }) => ({ type, name }))
// );
export const filterTools = (
  tools: DefinedTool[],
  query: string
): DefinedTool[] => {
  if (!query) return tools;

  const lowerCaseQuery = query.toLowerCase();

  return tools.filter(
    (tool) =>
      tool.name.toLowerCase().includes(lowerCaseQuery) ||
      tool.description.toLowerCase().includes(lowerCaseQuery) ||
      tool.shortDescription.toLowerCase().includes(lowerCaseQuery) ||
      tool.keywords.some((keyword) =>
        keyword.toLowerCase().includes(lowerCaseQuery)
      )
  );
};

export const getToolsByCategory = (): {
  title: string;
  rawTitle: string;
  description: string;
  icon: IconifyIcon | string;
  type: ToolCategory;
  example: { title: string; path: string };
  tools: DefinedTool[];
}[] => {
  const groupedByType: Partial<Record<ToolCategory, DefinedTool[]>> =
    Object.groupBy(tools, ({ type }) => type);
  return (Object.entries(groupedByType) as Entries<typeof groupedByType>)
    .map(([type, tools]) => {
      const categoryConfig = categoriesConfig.find(
        (config) => config.type === type
      );
      return {
        rawTitle: categoryConfig?.title ?? capitalizeFirstLetter(type),
        title: `${categoryConfig?.title ?? capitalizeFirstLetter(type)} Tools`,
        description: categoryConfig?.value ?? '',
        type,
        icon: categoryConfig!.icon,
        tools: tools ?? [],
        example: tools
          ? { title: tools[0].name, path: tools[0].path }
          : { title: '', path: '' }
      };
    })
    .sort(
      (a, b) =>
        toolCategoriesOrder.indexOf(a.type) -
        toolCategoriesOrder.indexOf(b.type)
    );
};
