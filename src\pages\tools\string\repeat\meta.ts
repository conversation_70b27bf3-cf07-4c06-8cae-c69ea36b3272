import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Repeat Text',
  path: 'repeat',
  icon: 'mdi:tools',
  description: 'This tool allows you to repeat a given text multiple times with an optional separator.',
  shortDescription: 'Repeat text multiple times',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
