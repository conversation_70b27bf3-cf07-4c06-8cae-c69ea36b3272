import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Repeat Text',
  path: 'repeat',
  icon: 'mdi:tools',
  description: 'Repeat Text tool for string operations. Perform repeat operations efficiently.',
  shortDescription: 'Repeat Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
