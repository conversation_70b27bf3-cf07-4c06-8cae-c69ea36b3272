import ToolContent from '@components/ToolContent';
import React, { useState } from 'react';
import ToolTextInput from '@components/input/ToolTextInput';
import ToolTextResult from '@components/result/ToolTextResult';
import { compute } from './service';
import TextFieldWithDesc from '@components/options/TextFieldWithDesc';
import { useTranslation } from 'react-i18next';
import { ToolComponentProps } from '@tools/defineTool';

const initialValues = {
  dotSymbol: '.',
  dashSymbol: '-'
};

export default function ToMorse({ title }: ToolComponentProps) {
  const { t } = useTranslation();
  const [input, setInput] = useState<string>('');
  const [result, setResult] = useState<string>('');
  const computeOptions = (optionsValues: typeof initialValues, input: any) => {
    const { dotSymbol, dashSymbol } = optionsValues;
    setResult(compute(input, dotSymbol, dashSymbol));
  };

  return (
    <ToolContent
      title={t('tools:string.to-morse.name')}
      initialValues={initialValues}
      compute={computeOptions}
      input={input}
      setInput={setInput}
      inputComponent={<ToolTextInput value={input} onChange={setInput} />}
      resultComponent={<ToolTextResult title={t('common.morseCode')} value={result} />}
      getGroups={({ values, updateField }) => [
        {
          title: t('common.shortSignal'),
          component: (
            <TextFieldWithDesc
              description={t('tools:string.to-morse.dotDescription')}
              value={values.dotSymbol}
              onOwnChange={(val) => updateField('dotSymbol', val)}
            />
          )
        },
        {
          title: t('common.longSignal'),
          component: (
            <TextFieldWithDesc
              description={t('tools:string.to-morse.dashDescription')}
              value={values.dashSymbol}
              onOwnChange={(val) => updateField('dashSymbol', val)}
            />
          )
        }
      ]}
    />
  );
}
