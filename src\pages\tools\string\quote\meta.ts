import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Quote Text',
  path: 'quote',
  icon: 'mdi:tools',
  description: 'A tool to add quotation marks or custom characters around text. Perfect for formatting strings for code, citations, or stylistic purposes.',
  shortDescription: 'Add quotes around text easily.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
