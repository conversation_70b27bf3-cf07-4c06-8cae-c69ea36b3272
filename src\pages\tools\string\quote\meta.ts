import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Quote Text',
  path: 'quote',
  icon: 'mdi:tools',
  description: 'Quote Text tool for string operations. Perform quote operations efficiently.',
  shortDescription: 'Quote Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
