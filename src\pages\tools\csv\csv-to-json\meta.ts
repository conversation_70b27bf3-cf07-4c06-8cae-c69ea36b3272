import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Tool',
  path: 'csv-to-json',
  icon: 'mdi:tools',
  description: 'Convert CSV files to JSON format with customizable options for delimiters, quotes, and output formatting. Support for headers, comments, and dynamic type conversion.',
  shortDescription: 'Convert CSV data to JSON format.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
