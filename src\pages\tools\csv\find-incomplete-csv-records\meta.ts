import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Find Incomplete Csv Records',
  path: 'find-incomplete-csv-records',
  icon: 'mdi:tools',
  description: 'Find Incomplete Csv Records tool for csv operations. Perform find incomplete csv records operations efficiently.',
  shortDescription: 'Find Incomplete Csv Records tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
