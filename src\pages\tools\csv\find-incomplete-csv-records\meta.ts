import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Tool',
  path: 'find-incomplete-csv-records',
  icon: 'mdi:tools',
  description: 'Just upload your CSV file in the form below and this tool will automatically check if none of the rows or columns are missing values. In the tool options, you can adjust the input file format (specify the delimiter, quote character, and comment character). Additionally, you can enable checking for empty values, skip empty lines, and set a limit on the number of error messages in the output.',
  shortDescription: 'Quickly find rows and columns in CSV that are missing values.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
