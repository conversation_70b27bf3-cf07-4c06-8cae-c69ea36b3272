import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Loop',
  path: 'loop',
  icon: 'mdi:tools',
  description: 'Loop tool for video operations. Perform loop operations efficiently.',
  shortDescription: 'Loop tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
