import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Reverse',
  path: 'reverse',
  icon: 'mdi:tools',
  description: 'Reverse tool for list operations. Perform reverse operations efficiently.',
  shortDescription: 'Reverse tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
