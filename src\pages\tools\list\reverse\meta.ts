import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Tool',
  path: 'reverse',
  icon: 'mdi:tools',
  description: 'This is a super simple browser-based application prints all list items in reverse. The input items can be separated by any symbol and you can also change the separator of the reversed list items.',
  shortDescription: 'Quickly reverse a list',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
