import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Uppercase Text',
  path: 'uppercase',
  icon: 'mdi:tools',
  description: 'Uppercase Text tool for string operations. Perform uppercase operations efficiently.',
  shortDescription: 'Uppercase Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
