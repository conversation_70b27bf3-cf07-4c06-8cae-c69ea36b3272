import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Rotate Text',
  path: 'rotate',
  icon: 'mdi:tools',
  description: 'A tool to rotate characters in a string by a specified number of positions. Shift characters left or right while maintaining their relative order.',
  shortDescription: 'Shift characters in text by position.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
