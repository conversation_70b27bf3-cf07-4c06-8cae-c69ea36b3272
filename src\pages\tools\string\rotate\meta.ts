import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Rotate Text',
  path: 'rotate',
  icon: 'mdi:tools',
  description: 'Rotate Text tool for string operations. Perform rotate operations efficiently.',
  shortDescription: 'Rotate Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
