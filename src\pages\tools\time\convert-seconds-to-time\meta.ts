import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Seconds to Time',
  path: 'convert-seconds-to-time',
  icon: 'mdi:tools',
  description: 'With this browser-based application, you can convert seconds to clock time. Given the seconds input value, it converts them into full hours (H), minutes (M), and seconds (S) and prints them in human-readable clock format (H:M:S or HH:MM:SS) in the output field.',
  shortDescription: 'Quicky convert seconds to clock time in H:M:S format.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
