import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Seconds to Time',
  path: 'convert-seconds-to-time',
  icon: 'mdi:tools',
  description: 'Seconds to Time tool for time operations. Perform convert seconds to time operations efficiently.',
  shortDescription: 'Seconds to Time tool for time operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
