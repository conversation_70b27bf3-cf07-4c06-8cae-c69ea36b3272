{"compilerOptions": {"baseUrl": "./src", "target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client", "vitest/globals", "@testing-library/jest-dom"], "paths": {"@tools/*": ["./tools/*"], "@assets/*": ["./assets/*"], "@components/*": ["./components/*"], "@utils/*": ["./utils/*"]}}, "include": ["src", "./@types"], "exclude": ["node_modules"]}