#!/usr/bin/env node

/**
 * Fix Meta Files Script
 * 
 * This script fixes meta.ts files by removing incorrect useTranslation imports
 * and reverting to hardcoded strings since meta files are not React components.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class MetaFixer {
  constructor() {
    this.fixedFiles = 0;
  }

  fixMetaFile(filePath) {
    console.log(`Fixing meta: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Remove useTranslation import
    if (content.includes("import { useTranslation } from 'react-i18next';")) {
      content = content.replace("import { useTranslation } from 'react-i18next';\n", '');
      modified = true;
    }
    
    // Remove translation function calls and restore hardcoded strings
    // We'll need to get the original descriptions from the translation files
    const toolPath = this.getToolPath(filePath);
    if (toolPath) {
      const translationKey = this.getTranslationKey(toolPath);
      
      // Try to get the original description from translation files
      try {
        const enTools = JSON.parse(fs.readFileSync('src/locales/en/tools.json', 'utf8'));
        const description = this.getNestedProperty(enTools, translationKey + '.description');
        const shortDescription = this.getNestedProperty(enTools, translationKey + '.shortDescription');
        
        if (description) {
          content = content.replace(
            `description: t('tools:${translationKey}.description')`,
            `description: '${description}'`
          );
          modified = true;
        }
        
        if (shortDescription) {
          content = content.replace(
            `shortDescription: t('tools:${translationKey}.shortDescription')`,
            `shortDescription: '${shortDescription}'`
          );
          modified = true;
        }
      } catch (error) {
        console.log(`⚠️  Could not restore descriptions for: ${filePath}`);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.fixedFiles++;
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`⏭️  No fixes needed: ${filePath}`);
    }
  }

  getToolPath(filePath) {
    const match = filePath.match(/src[\/\\]pages[\/\\]tools[\/\\](.+)[\/\\]meta\.ts$/);
    return match ? match[1].replace(/\\/g, '/') : null;
  }

  getTranslationKey(toolPath) {
    return toolPath.replace(/\//g, '.');
  }

  getNestedProperty(obj, path) {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return current;
  }

  fixAllMetaFiles() {
    console.log('🔧 Starting meta files fixes...\n');
    
    const files = glob.sync('src/pages/tools/**/meta.ts', {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    console.log(`Found ${files.length} meta files to fix\n`);
    
    files.forEach(file => {
      this.fixMetaFile(file);
    });
    
    console.log('\n📊 SUMMARY:');
    console.log(`Files fixed: ${this.fixedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const fixer = new MetaFixer();
  fixer.fixAllMetaFiles();
  console.log('\n✅ Meta files fixes completed!');
}

module.exports = MetaFixer;
