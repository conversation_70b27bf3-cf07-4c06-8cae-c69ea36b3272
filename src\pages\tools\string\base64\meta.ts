import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Base64 Encode/Decode',
  path: 'base64',
  icon: 'mdi:tools',
  description: 'A simple tool to encode or decode data using Base64, which is commonly used in web applications.',
  shortDescription: 'Encode or decode data using Base64.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
