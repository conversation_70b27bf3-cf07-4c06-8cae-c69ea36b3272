import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Base64 Encode/Decode',
  path: 'base64',
  icon: 'mdi:tools',
  description: 'Base64 Encode/Decode tool for string operations. Perform base64 operations efficiently.',
  shortDescription: 'Base64 Encode/Decode tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
