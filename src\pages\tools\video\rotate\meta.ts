import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Rotate',
  path: 'rotate',
  icon: 'mdi:tools',
  description: 'Rotate tool for video operations. Perform rotate operations efficiently.',
  shortDescription: 'Rotate tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
