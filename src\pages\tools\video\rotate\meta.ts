import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Tool',
  path: 'rotate',
  icon: 'mdi:tools',
  description: 'This online utility lets you rotate videos by 90, 180, or 270 degrees. You can preview the rotated video before processing. Supports common video formats like MP4, WebM, and OGG.',
  shortDescription: 'Rotate videos by 90, 180, or 270 degrees',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
