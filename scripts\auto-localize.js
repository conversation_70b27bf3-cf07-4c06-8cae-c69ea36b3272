#!/usr/bin/env node

/**
 * Auto-Localization Script for OmniTools
 * 
 * This script automatically adds localization to React components by:
 * 1. Adding useTranslation import and hook
 * 2. Replacing common hardcoded strings with translation keys
 * 3. Updating title props in ToolTextResult components
 * 4. Replacing hardcoded group titles in getGroups functions
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Common string replacements
const STRING_REPLACEMENTS = {
  // Result titles
  "'Text without duplicates'": "t('common.results.textWithoutDuplicates')",
  "'Randomized text'": "t('common.results.randomizedText')",
  "'Quoted Text'": "t('common.results.quotedText')",
  "'Reversed text'": "t('common.results.reversedText')",
  "'ROT13 Text'": "t('common.results.rot13Text')",
  "'Rotated Text'": "t('common.results.rotatedText')",
  "'Split Text'": "t('common.results.splitText')",
  "'Joined Text'": "t('common.results.joinedText')",
  "'Replaced Text'": "t('common.results.replacedText')",
  "'Repeated Text'": "t('common.results.repeatedText')",
  "'Truncated Text'": "t('common.results.truncatedText')",
  "'Text Statistics'": "t('common.results.textStatistics')",
  "'Palindrome Check Result'": "t('common.results.palindromeResult')",
  "'Extracted Substring'": "t('common.results.extractedSubstring')",
  "'Palindrome Text'": "t('common.results.palindromeText')",
  
  // Group titles
  "'Operation Mode'": "t('common.options.operationMode')",
  "'Newlines, Tabs and Spaces'": "t('common.options.newlinesTabsSpaces')",
  "'Sort Lines'": "t('common.options.sortLines')",
  "'Quote Options'": "t('common.options.quoteOptions')",
  "'Reversal options'": "t('common.options.reversalOptions')",
  "'Rotation Options'": "t('common.options.rotationOptions')",
  "'Split separator options'": "t('common.options.splitOptions')",
  "'Output separator options'": "t('common.options.outputOptions')",
  "'Text Merged Options'": "t('common.options.textMergedOptions')",
  "'Blank Lines and Trailing Spaces'": "t('common.options.blankLinesTrailingSpaces')",
  "'Text Repetitions'": "t('common.options.textRepetitions')",
  "'Repetitions Delimiter'": "t('common.options.repetitionsDelimiter')",
  "'Truncation Side'": "t('common.options.truncationSide')",
  "'Length and Lines'": "t('common.options.lengthAndLines')",
  "'Suffix and Affix'": "t('common.options.suffixAndAffix')",
  "'Delimiters Options'": "t('common.options.delimitersOptions')",
  "'Statistics Options'": "t('common.options.statisticsOptions')",
  "'Splitting options'": "t('common.options.splittingOptions')",
  "'Extraction options'": "t('common.options.extractionOptions')",
  "'Palindrome options'": "t('common.options.palindromeOptions')",
  
  // Input titles
  "'Input Text'": "t('common.inputText')",
  
  // Double quotes versions
  '"Text without duplicates"': "t('common.results.textWithoutDuplicates')",
  '"Randomized text"': "t('common.results.randomizedText')",
  '"Quoted Text"': "t('common.results.quotedText')",
  '"Reversed text"': "t('common.results.reversedText')",
  '"ROT13 Text"': "t('common.results.rot13Text')",
  '"Rotated Text"': "t('common.results.rotatedText')",
  '"Split Text"': "t('common.results.splitText')",
  '"Joined Text"': "t('common.results.joinedText')",
  '"Replaced Text"': "t('common.results.replacedText')",
  '"Repeated Text"': "t('common.results.repeatedText')",
  '"Truncated Text"': "t('common.results.truncatedText')",
  '"Text Statistics"': "t('common.results.textStatistics')",
  '"Palindrome Check Result"': "t('common.results.palindromeResult')",
  '"Extracted Substring"': "t('common.results.extractedSubstring')",
  '"Palindrome Text"': "t('common.results.palindromeText')",
  '"Operation Mode"': "t('common.options.operationMode')",
  '"Input Text"': "t('common.inputText')"
};

class AutoLocalizer {
  constructor() {
    this.processedFiles = 0;
    this.modifiedFiles = 0;
  }

  processFile(filePath) {
    console.log(`Processing: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check if file already uses useTranslation
    const hasUseTranslation = content.includes('useTranslation');
    
    if (!hasUseTranslation) {
      // Add useTranslation import
      if (content.includes("from 'react';")) {
        content = content.replace(
          "from 'react';",
          "from 'react';\nimport { useTranslation } from 'react-i18next';"
        );
        modified = true;
      }
      
      // Add useTranslation hook to component
      if (!content.includes('const { t } = useTranslation();')) {
        // Find the function declaration and add the hook after the opening brace
        const functionMatch = content.match(/(export default function \w+\([^)]*\) \{[\s\n]*)/);
        if (functionMatch) {
          content = content.replace(
            functionMatch[1],
            functionMatch[1] + '  const { t } = useTranslation();\n  '
          );
          modified = true;
        }
      }
    }
    
    // Replace hardcoded strings
    for (const [oldStr, newStr] of Object.entries(STRING_REPLACEMENTS)) {
      if (content.includes(oldStr)) {
        content = content.replace(new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newStr);
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    } else {
      console.log(`⏭️  Skipped: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  processDirectory(pattern) {
    console.log(`🔍 Searching for files: ${pattern}\n`);
    
    const files = glob.sync(pattern, {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    console.log(`Found ${files.length} files to process\n`);
    
    files.forEach(file => {
      this.processFile(file);
    });
    
    console.log('\n📊 SUMMARY:');
    console.log(`Total files processed: ${this.processedFiles}`);
    console.log(`Files modified: ${this.modifiedFiles}`);
    console.log(`Files skipped: ${this.processedFiles - this.modifiedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const localizer = new AutoLocalizer();
  
  console.log('🚀 Starting Auto-Localization...\n');
  
  // Process all tool index files
  localizer.processDirectory('src/pages/tools/**/index.tsx');
  
  console.log('\n✅ Auto-localization completed!');
}

module.exports = AutoLocalizer;
