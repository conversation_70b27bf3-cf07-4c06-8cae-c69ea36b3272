import { DefinedTool } from '../tools/defineTool';
import { getToolTranslation } from '../hooks/useToolTranslation';
import { getCurrentLanguage } from '../i18n';

export interface LocalizedTool extends DefinedTool {
  localizedName: string;
  localizedDescription: string;
  localizedShortDescription: string;
}

export const getLocalizedTool = (tool: DefinedTool, language?: string): LocalizedTool => {
  const currentLang = language || getCurrentLanguage();
  
  // Extract tool path from full path (remove category prefix)
  const toolPath = tool.path.split('/').pop() || '';
  
  // Get translation
  const translation = getToolTranslation(tool.type, toolPath, currentLang);
  
  return {
    ...tool,
    localizedName: translation.name || tool.name,
    localizedDescription: translation.description || tool.description,
    localizedShortDescription: translation.shortDescription || tool.shortDescription
  };
};

export const getLocalizedTools = (tools: DefinedTool[], language?: string): LocalizedTool[] => {
  return tools.map(tool => getLocalizedTool(tool, language));
};

export const filterLocalizedTools = (
  tools: DefinedTool[], 
  searchQuery: string, 
  language?: string
): LocalizedTool[] => {
  if (!searchQuery.trim()) {
    return getLocalizedTools(tools, language);
  }
  
  const query = searchQuery.toLowerCase();
  const localizedTools = getLocalizedTools(tools, language);
  
  return localizedTools.filter(tool => {
    // Search in localized name and description
    const searchableText = [
      tool.localizedName,
      tool.localizedShortDescription,
      tool.localizedDescription,
      // Also search in original text for fallback
      tool.name,
      tool.shortDescription,
      tool.description,
      // Search in keywords
      ...tool.keywords
    ].join(' ').toLowerCase();
    
    return searchableText.includes(query);
  });
};
