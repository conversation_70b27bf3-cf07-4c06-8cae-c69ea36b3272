import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'ValidateJson',
  path: 'validate<PERSON>son',
  icon: 'mdi:tools',
  description: 'ValidateJson tool for json operations. Perform validateJson operations efficiently.',
  shortDescription: 'ValidateJson tool for json operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
