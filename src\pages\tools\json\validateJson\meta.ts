import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Tool',
  path: 'validate<PERSON>son',
  icon: 'mdi:tools',
  description: 'Validate JSON data and identify formatting issues such as missing quotes, trailing commas, and incorrect brackets.',
  shortDescription: 'Quickly validate a JSON data structure.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
