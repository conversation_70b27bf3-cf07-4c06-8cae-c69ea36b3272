import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Truncate Text',
  path: 'truncate',
  icon: 'mdi:tools',
  description: 'Truncate Text tool for string operations. Perform truncate operations efficiently.',
  shortDescription: 'Truncate Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
