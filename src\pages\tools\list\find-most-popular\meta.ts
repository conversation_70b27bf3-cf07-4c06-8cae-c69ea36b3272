import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Find Most Popular',
  path: 'find-most-popular',
  icon: 'mdi:tools',
  description: 'Find Most Popular tool for list operations. Perform find most popular operations efficiently.',
  shortDescription: 'Find Most Popular tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
