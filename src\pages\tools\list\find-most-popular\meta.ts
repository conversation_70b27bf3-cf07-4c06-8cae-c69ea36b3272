import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Tool',
  path: 'find-most-popular',
  icon: 'mdi:tools',
  description: 'A tool to identify and count the most frequently occurring items in a list. Useful for data analysis, finding trends, or identifying common elements.',
  shortDescription: 'Find most common items in a list.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
