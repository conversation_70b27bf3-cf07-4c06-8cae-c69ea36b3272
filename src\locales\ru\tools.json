{"string": {"to-morse": {"name": "Текст в азбуку Морзе", "description": "World", "shortDescription": "Быстро encode текст в morse", "dotDescription": "Символ, который будет соответствовать точке в азбуке Морзе.", "dashDescription": "Символ, который будет соответствовать тире в азбуке Морзе."}, "uppercase": {"name": "Верхний регистр", "description": "World", "shortDescription": "Конвертировать текст в uppercase letters", "example1": {"title": "Преобразовать текст в верхний регистр", "description": "Этот пример преобразует любой текст в формат ВЕРХНЕГО РЕГИСТРА."}, "example2": {"title": "Код в верхнем регистре", "description": "Преобразовать код в формат верхнего регистра. Обратите внимание, что это только для отображения и не сохранит функциональность кода."}, "example3": {"title": "Смешанный регистр в верхний", "description": "Преобразовать текст со смешанным регистром в последовательный формат верхнего регистра."}}, "lowercase": {"name": "Нижний регистр", "description": "Преобразовать текст в строчные буквы", "shortDescription": "Преобразовать в нижний регистр"}, "base64": {"name": "Base64 кодирование/декодирование", "description": "A simple инструмент в encode или decode данные using Base64, which is commonly used in web applications.", "shortDescription": "Encode или decode данные using Base64.", "optionsTitle": "Настройки Base64", "encode": "Кодировать в Base64", "decode": "Декодировать из Base64", "example1": {"title": "Кодировать данные в UTF-8 с помощью Base64", "description": "Этот пример показывает, как кодировать простой текст с помощью Base64."}, "example2": {"title": "Декодировать данные из Base64 в UTF-8", "description": "Этот пример показывает, как декодировать данные, которые были закодированы с помощью Base64."}}, "rot13": {"name": "ROT13 кодирование/декодирование", "description": "A simple инструмент в encode или decode текст using the ROT13 cipher, which replaces each letter с the letter 13 positions after it in the alphabet.", "shortDescription": "Encode или decode текст using ROT13 cipher."}, "join": {"name": "Объединить текст", "description": "World", "shortDescription": "Быстро Объединить texts"}, "split": {"name": "Разделить текст", "description": "World", "shortDescription": "Быстро Разделить a текст"}, "randomize-case": {"name": "Случайный регистр", "description": "World", "shortDescription": "Конвертировать текст в random uppercase и lowercase letters"}, "palindrome": {"name": "Проверка палиндрома", "description": "World", "shortDescription": "Check if текст reads the same forward и backward"}, "remove-duplicate-lines": {"name": "Удалить дублирующиеся строки", "description": "Load your текст in the Ввод form on the left и you", "shortDescription": "Быстро delete all repeated lines из текст", "trimTextLines": "Обрезать строки текста", "trimTextLinesDesc": "Перед фильтрацией уникальных строк удалить табуляцию и пробелы в начале и конце всех строк.", "sortOutputLines": "Сортировать выходные строки", "sortOutputLinesDesc": "После удаления дубликатов отсортировать уникальные строки."}, "quote": {"name": "Заключить в кавычки", "description": "A инструмент в Добавить quotation marks или custom characters around текст. Perfect для formatting strings для code, citations, или stylistic purposes.", "shortDescription": "До<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> quotes around текст Легко."}, "reverse": {"name": "Обратить текст", "description": "World", "shortDescription": "Reverse any текст символ на символ"}, "rotate": {"name": "Повернуть текст", "description": "A инструмент в Повернуть characters in a string на a specified number of positions. Shift characters left или right while maintaining their relative order.", "shortDescription": "Shift characters in текст на position."}, "text-replacer": {"name": "Заменить текст", "description": "Легко replace specific текст in your content с this simple, browser-based инструмент. Just Ввод your текст, set the текст you want в replace и the replacement value, и instantly get the updated version.", "shortDescription": "Быстро replace текст in your content"}, "repeat": {"name": "Повторить текст", "description": "This инструмент allows you в repeat a given текст multiple times с an optional разделитель.", "shortDescription": "Repeat текст multiple times"}, "truncate": {"name": "Обрезать текст", "description": "Load your текст in the Ввод form on the left и you will automatically get truncated текст on the right.", "shortDescription": "Truncate your текст Легко"}, "statistic": {"name": "Статистика текста", "description": "Load your текст in the Ввод form on the left и you will automatically get statistics about your текст on the right.", "shortDescription": "Get statistics about your текст"}, "extract-substring": {"name": "Извлечь подстроку", "description": "World", "shortDescription": "Извлечь specific portions of текст на position и length"}, "create-palindrome": {"name": "Создать палиндром", "description": "World", "shortDescription": "Create текст that reads the same forward и backward"}}, "csv": {"format": {"name": "Форматировать CSV", "description": "Форматировать и проверить CSV файлы", "shortDescription": "Форматировать CSV файлы"}, "change-csv-separator": {"description": "Just Загрузить your CSV file in the form below и it will automatically get a new column разделитель символ. In the инструмент Настройки, you can specify which разделитель и quote characters are used in the source CSV file и customize the desired разделитель и quote characters для the Вывод CSV. You can also filter the Ввод CSV before the conversion Обработать и skip blank lines и comment lines.", "shortDescription": "Быстро change the CSV column разделитель в a new symbol."}, "csv-rows-to-columns": {"description": "This инструмент converts rows of a CSV (Comma Separated Values) file into columns. It extracts the horizontal lines из the Ввод CSV one на one, rotates them 90 degrees, и outputs them as vertical columns one after another, separated на commas.", "shortDescription": "Конвертировать CSV rows в columns."}, "csv-to-json": {"description": "Конвертировать CSV файлы в JSON формат с customizable Настройки для delimiters, quotes, и Вывод formatting. Support для headers, comments, и dynamic type conversion.", "shortDescription": "Конвертировать CSV данные в JSON формат."}, "csv-to-tsv": {"description": "Загрузить your CSV file in the form below и it will automatically get converted в a TSV file. In the инструмент Настройки, you can customize the Ввод CSV формат – specify the field разделитель, quotation символ, и comment symbol, as well as skip empty CSV lines, и Выберите whether в preserve CSV column headers.", "shortDescription": "Конвертировать CSV данные в TSV формат."}, "csv-to-xml": {"description": "Конвертировать CSV файлы в XML формат с customizable Настройки.", "shortDescription": "Конвертировать CSV данные в XML формат."}, "csv-to-yaml": {"description": "Just Загрузить your CSV file in the form below и it will automatically get converted в a YAML file. In the инструмент Настройки, you can specify the field разделитель символ, field quote символ, и comment символ в adapt the инструмент в custom CSV formats. Additionally, you can Выберите Вывод YAML формат: one that preserves CSV headers или one that excludes CSV headers.", "shortDescription": "Быстро Конвертировать a CSV file в a YAML file."}, "find-incomplete-csv-records": {"description": "Just Загрузить your CSV file in the form below и this инструм<PERSON><PERSON><PERSON> will automatically check if none of the rows или columns are missing values. In the инструмент Настройки, you can adjust the Ввод file формат (specify the разделитель, quote символ, и comment символ). Additionally, you can enable checking для empty values, skip empty lines, и set a limit on the number of error messages in the Вывод.", "shortDescription": "Быстро find rows и columns in CSV that are missing values."}, "insert-csv-columns": {"description": "Just Загрузить your CSV file in the form below, paste the new column in the Настройки, и it will automatically get inserted in your CSV. In the инструмент Настройки, you can also specify more than one column в insert, set the insertion position, и optionally skip the empty и comment lines.", "shortDescription": "Быстро insert one или more new columns anywhere in a CSV file."}, "swap-csv-columns": {"description": "Just Загрузить your CSV file in the form below, specify the columns в swap, и the инструмен<PERSON> will automatically change the positions of the specified columns in the Вывод file. In the инструмент Настройки, you can specify the column positions или names that you want в swap, as well as fix incomplete данные и optionally Удалить empty records и records that have been commented out.", "shortDescription": "Reorder CSV columns."}, "transpose-csv": {"description": "Just Загрузить your CSV file in the form below, и this инструмен<PERSON> will automatically transpose your CSV. In the инструмент Настройки, you can specify the символ that starts the comment lines in the CSV в Удалить them. Additionally, if the CSV is incomplete (missing values), you can replace missing values с the empty символ или a custom символ.", "shortDescription": "Быстро transpose a CSV file."}}, "image-generic": {"image-to-text": {"name": "Изображение в текст (OCR)", "description": "Извлечь текст из изображений с помощью OCR", "shortDescription": "Извлечь текст из изображений"}, "resize": {"name": "Изменить размер изображения", "description": "Изменить размер изображений до определенных размеров", "shortDescription": "Изменить размер изображения"}, "compress": {"name": "Сжать изображение", "description": "Уменьшить размер файла изображения с сохранением качества", "shortDescription": "Сжать размер изображения"}, "remove-background": {"name": "Удалить фон", "description": "Автоматически удалить фон с изображений", "shortDescription": "Удалить фон изображения"}, "crop": {"name": "Обрезать изображение", "description": "Обрезать изображения до определенных областей", "shortDescription": "Обрезать область изображения"}, "change-opacity": {"name": "Изменить прозрачность", "description": "Настроить прозрачность/непрозрачность изображения", "shortDescription": "Изменить прозрачность изображения"}, "change-colors": {"name": "Изменить цвета", "description": "Изменить цвета в изображениях", "shortDescription": "Изменить цвета изображения"}, "create-transparent": {"name": "Создать прозрачное", "description": "Сделать определенные цвета прозрачными в изображениях", "shortDescription": "Создать прозрачное изображение"}, "qr-code": {"name": "Генератор QR-кодов", "description": "Генерировать QR-коды для различных типов данных", "shortDescription": "Генерировать QR-коды"}}, "number": {"sum": {"name": "Сумма чисел", "description": "Быстро calculate the sum of numbers in your browser. в get your sum, just enter your list of numbers in the Ввод field, adjust the разделитель between the numbers in the Настройки below, и this utility will Добавить up all these numbers.", "shortDescription": "Быстро sum numbers"}, "generate": {"name": "Генерировать числа", "description": "Быстро calculate a list of integers in your browser. в get your list, just specify the first integer, change value и total count in the Настройки below, и this utility will Генерировать that many integers", "shortDescription": "Быстро calculate a list of integers in your browser"}, "arithmetic-sequence": {"description": "Генерировать an arithmetic sequence на specifying the first term (a₁), common difference (d), и number of terms (n). The инструмент creates a sequence where each number differs из the previous на a constant difference.", "shortDescription": "Генерировать a sequence where each term differs на a constant value."}}, "list": {"sort": {"name": "Сортировать список", "description": "This is a super simple browser-based application that sorts items in a list и arranges them in increasing или decreasing order. You can sort the items alphabetically, numerically, или на their length. You can also Удалить duplicate и empty items, as well as trim individual items that have whitespace around them. You can use any разделитель символ в separate the Ввод list items или alternatively use a regular expression в separate them. Additionally, you can create a new разделитель для the sorted Вывод list.", "shortDescription": "Быстро sort a list"}, "shuffle": {"name": "Перемешать список", "description": "A инструмент в randomly reorder items in a list. Perfect для randomizing данные, creating random selections, или generating random sequences.", "shortDescription": "Randomly reorder list items."}, "find-unique": {"name": "Найти уникальные элементы", "description": "World", "shortDescription": "Find unique items in a list"}, "group": {"name": "Группировать список", "description": "World", "shortDescription": "Group list items на common properties"}, "unwrap": {"name": "Развернуть список", "description": "A инструмент в Удалить characters из the beginning и end of each item in a list. Perfect для cleaning up formatted данные или removing unwanted wrappers.", "shortDescription": "Удалить characters around list items."}, "rotate": {"name": "Повернуть список", "description": "A инструмент в Повернуть items in a list на a specified number of positions. Shift elements left или right while maintaining their relative order.", "shortDescription": "Shift list items на position."}, "duplicate": {"description": "A инструмент в duplicate each item in a list a specified number of times. Perfect для creating repeated patterns, test данные, или expanding datasets.", "shortDescription": "Repeat items in a list multiple times."}, "find-most-popular": {"description": "A инструмент в identify и count the most frequently occurring items in a list. Useful для данные analysis, finding trends, или identifying common elements.", "shortDescription": "Find most common items in a list."}, "reverse": {"description": "This is a super simple browser-based application prints all list items in reverse. The Ввод items can be separated на any symbol и you can also change the разделитель of the reversed list items.", "shortDescription": "Быстро reverse a list"}, "truncate": {"description": "World", "shortDescription": "Limit the number of items in a list"}, "wrap": {"description": "A инструмент в wrap each item in a list с custom prefix и suffix characters. Useful для formatting lists для code, markup languages, или presentation.", "shortDescription": "Добавить characters around list items."}}, "json": {"prettify": {"name": "Форматировать JSON", "description": "Just load your JSON in the Ввод field и it will automatically get prettified. In the инструмент Настройки, you can Выберите whether в use spaces или tabs для indentation и if you", "shortDescription": "Быстро beautify a JSON данные structure."}, "escape-json": {"description": "Free online JSON escaper. Just load your JSON in the Ввод field и it will automatically get escaped. In the инструмент Настройки, you can optionally enable wrapping the escaped JSON in double quotes в get an escaped JSON string.", "shortDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> escape special JSON characters."}, "json-to-xml": {"description": "Конвертировать JSON файлы в XML формат с customizable Настройки.", "shortDescription": "Конвертировать JSON данные в XML формат"}, "minify": {"description": "Minify your JSON на removing all unnecessary whitespace и formatting. This инструмент compresses JSON данные в its smallest possible size while maintaining valid JSON structure.", "shortDescription": "Быстро Сжать JSON file."}, "stringify": {"description": "Конвертировать JavaScript objects и arrays into their JSON string representation. Настройки include custom indentation и HTML символ escaping для web-safe JSON strings.", "shortDescription": "Конвертировать JavaScript objects в JSON strings"}, "tsv-to-json": {"description": "Конвертировать TSV файлы в JSON формат с customizable Настройки для delimiters, quotes, и Вывод formatting. Support для headers, comments, и dynamic type conversion.", "shortDescription": "Конвертировать TSV данные в JSON формат."}, "validateJson": {"description": "Validate JSON данные и identify formatting issues such as missing quotes, trailing commas, и incorrect brackets.", "shortDescription": "Быстро validate a JSON данные structure."}}, "pdf": {"split-pdf": {"name": "Разделить PDF", "description": "Извлечь specific pages из a PDF file using page numbers или ranges (e.g., 1,5-8)", "shortDescription": "Извлечь specific pages из a PDF file"}, "merge-pdf": {"name": "Объединить PDF", "description": "Combine multiple PDF файлы into a single document.", "shortDescription": "Объединить multiple PDF файлы into a single document"}, "rotate-pdf": {"name": "Повернуть PDF", "description": "Повернуть PDF pages на 90, 180, или 270 degrees", "shortDescription": "Повернуть pages in a PDF document"}, "compress-pdf": {"name": "Сжать PDF", "description": "Reduce PDF file size while maintaining качество using Ghostscript", "shortDescription": "Сжать PDF файлы securely in your browser"}, "protect-pdf": {"name": "Защитить PDF", "description": "Добавить password protection в your PDF файлы securely in your browser", "shortDescription": "Password protect PDF файлы securely"}, "pdf-to-epub": {"description": "Преобразовать PDF documents into EPUB файлы для better e-reader compatibility.", "shortDescription": "Конвертировать PDF файлы в EPUB формат"}}, "time": {"time-between-dates": {"name": "Время между датами", "description": "Calculate the exact time difference between two dates и times, с support для different timezones. This инструмент provides a detailed breakdown of the time difference in various units (years, months, days, hours, minutes, и seconds).", "shortDescription": "Calculate the precise time duration between two dates с timezone support."}, "convert-days-to-hours": {"name": "Дни в часы", "description": "с this browser-based application, you can calculate how many hours there are in the given number of days. The application takes the Ввод values (days), multiplies them на 24 и that converts them into hours. It supports both integer и decimal day values и it can Конвертировать multiple values at the same time.", "shortDescription": "Конвертировать days в hours Легко."}, "convert-hours-to-days": {"name": "Часы в дни", "description": "с this browser-based application, you can calculate how many days there are in the given number of hours. Given one или more hour values in the Ввод, it converts them into days via the simple math formula: days = hours/24. It works с arbitrary large hour values и you can also customize the decimal day precision.", "shortDescription": "Конвертировать hours в days Легко."}, "convert-seconds-to-time": {"name": "Секунды во время", "description": "с this browser-based application, you can Конвертировать seconds в clock time. Given the seconds Ввод value, it converts them into full hours (H), minutes (M), и seconds (S) и prints them in human-readable clock формат (H:M:S или HH:MM:SS) in the Вывод field.", "shortDescription": "Quicky Конвертировать seconds в clock time in H:M:S формат."}, "convert-time-to-seconds": {"name": "Время в секунды", "description": "с this browser-based application, you can Конвертировать clock time provided in hours, minutes, и seconds into just seconds. Given a time in HH:MM:SS формат, it calculates HH*3600 + MM*60 + SS и prints this value in the Вывод box. It supports AM/PM time formats as well as clock times beyond 24 hours.", "shortDescription": "Быстро Конвертировать clock time in H:M:S формат в seconds."}, "truncate-clock-time": {"name": "Усечь время", "description": "с this browser-based application, you can truncate a clock time и drop the minutes и/или seconds components из it. If you drop the seconds, you will be left с hours и minutes. для example, ", "shortDescription": "Быстро Конвертировать clock time in H:M:S формат в seconds."}}, "video": {"trim": {"name": "Обрезать видео", "description": "This online utility lets you trim videos на setting start и end points. You can preview the trimmed section before processing. Supports common video formats like MP4, WebM, и OGG.", "shortDescription": "Trim videos на setting start и end points"}, "change-speed": {"description": "This online utility lets you change the speed of a video. You can speed it up или slow it down.", "shortDescription": "Быстро change video speed"}, "compress": {"description": "Сжать videos на scaling them в different resolutions like 240p, 480p, 720p, etc. This инструмент helps reduce file size while maintaining acceptable качество. Supports common video formats like MP4, WebM, и OGG.", "shortDescription": "Сжать videos на scaling в different resolutions"}, "crop-video": {"description": "Обрезать a video на specifying coordinates и размеры", "shortDescription": "Обрезать video в specific area"}, "flip": {"description": "This online utility allows you в flip videos horizontally или vertically. You can preview the flipped video before processing. Supports common video formats like MP4, WebM, и OGG.", "shortDescription": "Flip videos horizontally или vertically"}, "gif": {"change-speed": {"description": "This online utility lets you change the speed of a GIF animation. You can speed it up или slow it down. You can set the same constant delay between all frames или change the delays of individual frames. You can also play both the Ввод и Вывод GIFs at the same time и compare their speeds", "shortDescription": "Быстро change GIF speed"}}, "loop": {"description": "This online utility lets you loop videos на specifying the number of repetitions. You can preview the looped video before processing. Supports common video formats like MP4, WebM, и OGG.", "shortDescription": "Loop videos multiple times"}, "rotate": {"description": "This online utility lets you Повернуть videos на 90, 180, или 270 degrees. You can preview the rotated video before processing. Supports common video formats like MP4, WebM, и OGG.", "shortDescription": "Повернуть videos на 90, 180, или 270 degrees"}}, "gif": {"change-speed": {"name": "Изменить скорость GIF", "description": "Изменить скорость GIF анимации", "shortDescription": "Изменить скорость GIF"}}, "png": {"compress-png": {"name": "Сжать PNG", "description": "Сжать PNG изображения для уменьшения размера файла", "shortDescription": "Сжать PNG файлы"}, "convert-jgp-to-png": {"name": "Конвертировать JPG в PNG", "description": "Конвертировать JPG изображения в формат PNG", "shortDescription": "Конвертировать JPG в PNG"}}, "image": {"generic": {"change-colors": {"description": "World", "shortDescription": "Быстро swap colors in a изображение"}, "change-opacity": {"description": "Легко adjust the transparency of your изображения. Просто Загрузить your изображение, use the slider в set the desired opacity level between 0 (fully transparent) и 1 (fully opaque), и Скачать the modified изображение.", "shortDescription": "Adjust transparency of изображения"}, "compress": {"description": "Сжать изображения в reduce file size while maintaining reasonable качество.", "shortDescription": "Сжать изображения в reduce file size while maintaining reasonable качество."}, "create-transparent": {"description": "World", "shortDescription": "Быстро make an изображение transparent"}, "crop": {"description": "A инструмент в Обрезать изображения с precision и ease.", "shortDescription": "Обрезать изображения Быстро."}, "image-to-text": {"description": "Извлечь текст из изображения (JPG, PNG) using optical символ recognition (OCR).", "shortDescription": "Извлечь текст из изображения using OCR."}, "qr-code": {"description": "Генерировать QR codes для different данные types: URL, текст, Email, Phone, SMS, WiFi, vCard, и more.", "shortDescription": "Create customized QR codes для various данные formats."}, "remove-background": {"description": "World", "shortDescription": "Automatically Удалить backgrounds из изображения"}, "resize": {"description": "Изменить размер JPG, PNG, SVG или GIF изображения на pixels или percentage while maintaining соотношение сторон или not.", "shortDescription": "Изменить размер изображения Легко."}}, "png": {"compress-png": {"description": "This is a program that compresses PNG pictures. As soon as you paste your PNG picture in the Ввод area, the program will Сжать it и show the Результат in the Вывод area. In the Настройки, you can adjust the сжатие level, as well as find the old и new picture file sizes.", "shortDescription": "Быстро Сжать a PNG"}, "convert-jgp-to-png": {"description": "Быстро Конвертировать your JPG изображения в PNG. Just import your PNG изображение in the editor on the left", "shortDescription": "Быстро Конвертировать your JPG изображения в PNG"}}}}