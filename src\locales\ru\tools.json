{"string": {"to-morse": {"name": "Текст в азбуку Морзе", "description": "Простейшая браузерная утилита для преобразования текста в азбуку Морзе. Загрузите текст в форму ввода слева, и вы мгновенно получите код Морзе в области вывода. Мощный, бесплатный и быстрый. Загрузите текст — получите код Морзе.", "shortDescription": "Быстро кодировать текст в азбуку Морзе", "dotDescription": "Символ, который будет соответствовать точке в азбуке Морзе.", "dashDescription": "Символ, который будет соответствовать тире в азбуке Морзе."}, "uppercase": {"name": "Верхний регистр", "description": "Преобразовать текст в заглавные буквы", "shortDescription": "Преобразовать в верхний регистр", "example1": {"title": "Преобразовать текст в верхний регистр", "description": "Этот пример преобразует любой текст в формат ВЕРХНЕГО РЕГИСТРА."}, "example2": {"title": "Код в верхнем регистре", "description": "Преобразовать код в формат верхнего регистра. Обратите внимание, что это только для отображения и не сохранит функциональность кода."}, "example3": {"title": "Смешанный регистр в верхний", "description": "Преобразовать текст со смешанным регистром в последовательный формат верхнего регистра."}}, "lowercase": {"name": "Нижний регистр", "description": "Преобразовать текст в строчные буквы", "shortDescription": "Преобразовать в нижний регистр"}, "base64": {"name": "Base64 кодирование/декодирование", "description": "Кодировать или декодировать текст с использованием Base64", "shortDescription": "Base64 кодирование/декодирование", "optionsTitle": "Настройки Base64", "encode": "Кодировать в Base64", "decode": "Декодировать из Base64", "example1": {"title": "Кодировать данные в UTF-8 с помощью Base64", "description": "Этот пример показывает, как кодировать простой текст с помощью Base64."}, "example2": {"title": "Декодировать данные из Base64 в UTF-8", "description": "Этот пример показывает, как декодировать данные, которые были закодированы с помощью Base64."}}, "rot13": {"name": "ROT13 кодирование/декодирование", "description": "Кодировать или декодировать текст с помощью шифра ROT13", "shortDescription": "Шифр ROT13"}, "join": {"name": "Объединить текст", "description": "Объединить части текста вместе", "shortDescription": "Объединить текст"}, "split": {"name": "Разделить текст", "description": "Разделить текст на несколько частей", "shortDescription": "Разделить текст"}, "randomize-case": {"name": "Случайный регистр", "description": "Случайно изменить регистр букв в тексте", "shortDescription": "Случайный регистр текста"}, "palindrome": {"name": "Проверка палиндрома", "description": "Проверить, является ли текст палиндромом", "shortDescription": "Проверить палиндром"}, "remove-duplicate-lines": {"name": "Удалить дублирующиеся строки", "description": "Удалить дублирующиеся строки из текста", "shortDescription": "Удалить дубликаты строк", "trimTextLines": "Обрезать строки текста", "trimTextLinesDesc": "Перед фильтрацией уникальных строк удалить табуляцию и пробелы в начале и конце всех строк.", "sortOutputLines": "Сортировать выходные строки", "sortOutputLinesDesc": "После удаления дубликатов отсортировать уникальные строки."}, "quote": {"name": "Заключить в кавычки", "description": "Добавить кавычки вокруг текста", "shortDescription": "Заключить в кавычки"}, "reverse": {"name": "Обратить текст", "description": "Обратить текст посимвольно", "shortDescription": "Обратить текст"}, "rotate": {"name": "Повернуть текст", "description": "Повернуть символы в тексте на указанное количество позиций", "shortDescription": "Повернуть символы текста"}, "text-replacer": {"name": "Заменить текст", "description": "Заменить определенный текст новым текстом", "shortDescription": "Заменить текст"}, "repeat": {"name": "Повторить текст", "description": "Повторить текст несколько раз", "shortDescription": "Повторить текст"}, "truncate": {"name": "Обрезать текст", "description": "Обрезать текст до указанной длины", "shortDescription": "Обрезать текст"}, "statistic": {"name": "Статистика текста", "description": "Получить подробную статистику о тексте", "shortDescription": "Статистика текста"}, "extract-substring": {"name": "Извлечь подстроку", "description": "Извлечь подстроку из текста", "shortDescription": "Извлечь подстроку"}, "create-palindrome": {"name": "Создать палиндром", "description": "Создать палиндром из текста", "shortDescription": "Создать палиндром"}}, "csv": {"format": {"name": "Форматировать CSV", "description": "Форматировать и проверить CSV файлы", "shortDescription": "Форматировать CSV файлы"}}, "image-generic": {"resize": {"name": "Изменить размер изображения", "description": "Изменить размер изображений до определенных размеров", "shortDescription": "Изменить размер изображения"}, "compress": {"name": "Сжать изображение", "description": "Уменьшить размер файла изображения с сохранением качества", "shortDescription": "Сжать размер изображения"}, "remove-background": {"name": "Удалить фон", "description": "Автоматически удалить фон с изображений", "shortDescription": "Удалить фон изображения"}, "crop": {"name": "Обрезать изображение", "description": "Обрезать изображения до определенных областей", "shortDescription": "Обрезать область изображения"}, "change-opacity": {"name": "Изменить прозрачность", "description": "Настроить прозрачность/непрозрачность изображения", "shortDescription": "Изменить прозрачность изображения"}, "change-colors": {"name": "Изменить цвета", "description": "Изменить цвета в изображениях", "shortDescription": "Изменить цвета изображения"}, "create-transparent": {"name": "Создать прозрачное", "description": "Сделать определенные цвета прозрачными в изображениях", "shortDescription": "Создать прозрачное изображение"}, "image-to-text": {"name": "Изображение в текст (OCR)", "description": "Извлечь текст из изображений с помощью OCR", "shortDescription": "Извлечь текст из изображений"}, "qr-code": {"name": "Генератор QR-кодов", "description": "Генерировать QR-коды для различных типов данных", "shortDescription": "Генерировать QR-коды"}}, "number": {"sum": {"name": "Сумма чисел", "description": "Вычислить сумму чисел", "shortDescription": "Вычислить сумму чисел"}, "generate": {"name": "Генерировать числа", "description": "Генерировать последовательности чисел", "shortDescription": "Генерировать числовые последовательности"}}, "list": {"sort": {"name": "Сортировать список", "description": "Сортировать элементы списка в различном порядке", "shortDescription": "Сортировать элементы списка"}, "shuffle": {"name": "Перемешать список", "description": "Случайно перемешать элементы списка", "shortDescription": "Случайно перемешать список"}, "find-unique": {"name": "Найти уникальные элементы", "description": "Найти уникальные элементы в списке", "shortDescription": "Найти уникальные элементы"}, "group": {"name": "Группировать список", "description": "Группировать элементы списка в блоки", "shortDescription": "Группировать элементы списка"}, "unwrap": {"name": "Развернуть список", "description": "Удалить обрамляющие символы из элементов списка", "shortDescription": "Развернуть элементы списка"}, "rotate": {"name": "Повернуть список", "description": "Повернуть элементы списка влево или вправо", "shortDescription": "Повернуть элементы списка"}}, "json": {"prettify": {"name": "Форматировать JSON", "description": "Форматировать и украсить JSON данные", "shortDescription": "Форматировать JSON данные"}}, "pdf": {"split-pdf": {"name": "Разделить PDF", "description": "Разделить PDF файлы на отдельные страницы", "shortDescription": "Разделить страницы PDF"}, "merge-pdf": {"name": "Объединить PDF", "description": "Объединить несколько PDF файлов в один", "shortDescription": "Объединить PDF файлы"}, "rotate-pdf": {"name": "Повернуть PDF", "description": "Повернуть страницы PDF", "shortDescription": "Повернуть страницы PDF"}, "compress-pdf": {"name": "Сжать PDF", "description": "Уменьшить размер PDF файла", "shortDescription": "Сжать размер PDF"}, "protect-pdf": {"name": "Защитить PDF", "description": "Добавить защиту паролем к PDF", "shortDescription": "Защитить PDF паролем"}}, "time": {"time-between-dates": {"name": "Время между датами", "description": "Вычислить разность времени между датами", "shortDescription": "Вычислить разность дат"}, "convert-days-to-hours": {"name": "Дни в часы", "description": "Преобразовать дни в часы", "shortDescription": "Преобразовать дни в часы"}, "convert-hours-to-days": {"name": "Часы в дни", "description": "Преобразовать часы в дни", "shortDescription": "Преобразовать часы в дни"}, "convert-seconds-to-time": {"name": "Секунды во время", "description": "Преобразовать секунды в формат времени", "shortDescription": "Преобразовать секунды во время"}, "convert-time-to-seconds": {"name": "Время в секунды", "description": "Преобразовать формат времени в секунды", "shortDescription": "Преобразовать время в секунды"}, "truncate-clock-time": {"name": "Усечь время", "description": "Усечь время до определенной точности", "shortDescription": "Усечь время часов"}}, "video": {"trim": {"name": "Обрезать видео", "description": "Обрезать видео до определенной продолжительности", "shortDescription": "Обрезать продолжительность видео"}}, "gif": {"change-speed": {"name": "Изменить скорость GIF", "description": "Изменить скорость GIF анимации", "shortDescription": "Изменить скорость GIF"}}, "png": {"compress-png": {"name": "Сжать PNG", "description": "Сжать PNG изображения для уменьшения размера файла", "shortDescription": "Сжать PNG файлы"}, "convert-jgp-to-png": {"name": "Конвертировать JPG в PNG", "description": "Конвертировать JPG изображения в формат PNG", "shortDescription": "Конвертировать JPG в PNG"}}}