{"string": {"to-morse": {"name": "String To Morse", "description": "String To Morse tool for string operations. Perform to morse operations efficiently.", "shortDescription": "String To Morse tool for string operations.", "dotDescription": "Symbol that will correspond to the dot in Morse code.", "dashDescription": "Symbol that will correspond to the dash in Morse code."}, "uppercase": {"name": "Uppercase Text", "description": "Uppercase Text tool for string operations. Perform uppercase operations efficiently.", "shortDescription": "Uppercase Text tool for string operations.", "example1": {"title": "Преобразовать Text to Uppercase", "description": "This example transforms any text to ALL UPPERCASE format."}, "example2": {"title": "Uppercase Code", "description": "Преобразовать code to uppercase format. Note that this is for display only and would not maintain code functionality."}, "example3": {"title": "Mixed Case to Uppercase", "description": "Transform text with mixed casing to consistent all uppercase format."}}, "lowercase": {"name": "Lowercase Text", "description": "Преобразовать text to lowercase letters", "shortDescription": "Преобразовать text to lowercase"}, "base64": {"name": "Base64 Кодировать/Декодировать", "description": "Base64 Кодировать/Декодировать tool for string operations. Perform base64 operations efficiently.", "shortDescription": "Base64 Кодировать/Декодировать tool for string operations.", "optionsTitle": "Base64 Options", "encode": "Base64 Кодировать", "decode": "Base64 Декодировать", "example1": {"title": "Кодировать data in UTF-8 with Base64", "description": "This example shows how to encode a simple text using Base64."}, "example2": {"title": "Декодировать Base64-encoded data to UTF-8", "description": "This example shows how to decode data that was encoded with Base64."}}, "rot13": {"name": "ROT13 Кодировать/Декодировать", "description": "ROT13 Кодировать/Декодировать tool for string operations. Perform rot13 operations efficiently.", "shortDescription": "ROT13 Кодировать/Декодировать tool for string operations."}, "join": {"name": "Join Text", "description": "Join Text tool for string operations. Perform join operations efficiently.", "shortDescription": "Join Text tool for string operations."}, "split": {"name": "Split Text", "description": "Split Text tool for string operations. Perform split operations efficiently.", "shortDescription": "Split Text tool for string operations."}, "randomize-case": {"name": "Randomize Case", "description": "Randomize Case tool for string operations. Perform randomize case operations efficiently.", "shortDescription": "Randomize Case tool for string operations."}, "palindrome": {"name": "Провери<PERSON>ь <PERSON>", "description": "Проверить Palindrome tool for string operations. Perform palindrome operations efficiently.", "shortDescription": "Проверить Palindrome tool for string operations."}, "remove-duplicate-lines": {"name": "Удалить Duplicate Lines", "description": "Удалить Duplicate Lines tool for string operations. Perform remove duplicate lines operations efficiently.", "shortDescription": "Удалить Duplicate Lines tool for string operations.", "trimTextLines": "Trim Text Lines", "trimTextLinesDesc": "Before filtering uniques, remove tabs and spaces from the beginning and end of all lines.", "sortOutputLines": "Сортировать the Output Lines", "sortOutputLinesDesc": "After removing the duplicates, sort the unique lines."}, "quote": {"name": "Quote Text", "description": "Quote Text tool for string operations. Perform quote operations efficiently.", "shortDescription": "Quote Text tool for string operations."}, "reverse": {"name": "Обратить Text", "description": "Обратить Text tool for string operations. Perform reverse operations efficiently.", "shortDescription": "Обратить Text tool for string operations.", "example1": {"title": "Простое обращение текста", "description": "Обращает каждый символ в тексте. Идеально для создания зеркального текста."}, "example2": {"title": "Многострочное обращение", "description": "Обращает каждую строку независимо, сохраняя переносы строк."}, "example3": {"title": "Очищенный обращенный текст", "description": "Удаляет пробелы и пропускает пустые строки перед обращением текста."}}, "rotate": {"name": "Rotate Text", "description": "Rotate Text tool for string operations. Perform rotate operations efficiently.", "shortDescription": "Rotate Text tool for string operations."}, "text-replacer": {"name": "Заменить Text", "description": "Заменить Text tool for string operations. Perform text replacer operations efficiently.", "shortDescription": "Заменить Text tool for string operations."}, "repeat": {"name": "Repeat Text", "description": "Repeat Text tool for string operations. Perform repeat operations efficiently.", "shortDescription": "Repeat Text tool for string operations."}, "truncate": {"name": "Truncate Text", "description": "Truncate Text tool for string operations. Perform truncate operations efficiently.", "shortDescription": "Truncate Text tool for string operations."}, "statistic": {"name": "Text Statistics", "description": "Text Statistics tool for string operations. Perform statistic operations efficiently.", "shortDescription": "Text Statistics tool for string operations."}, "extract-substring": {"name": "Извлечь Substring", "description": "Извлечь Substring tool for string operations. Perform extract substring operations efficiently.", "shortDescription": "Извлечь Substring tool for string operations."}, "create-palindrome": {"name": "Create Palindrome", "description": "Create Palindrome tool for string operations. Perform create palindrome operations efficiently.", "shortDescription": "Create Palindrome tool for string operations."}}, "csv": {"format": {"name": "Форматировать CSV", "description": "Форматировать and validate CSV files", "shortDescription": "Форматировать CSV files"}, "change-csv-separator": {"description": "Change Csv Separator tool for csv operations. Perform change csv separator operations efficiently.", "shortDescription": "Change Csv Separator tool for csv operations.", "name": "Change Csv Separator"}, "csv-rows-to-columns": {"description": "Csv Rows To Columns tool for csv operations. Perform csv rows to columns operations efficiently.", "shortDescription": "Csv Rows To Columns tool for csv operations.", "name": "Csv Rows To Columns"}, "csv-to-json": {"description": "Csv To Json tool for csv operations. Perform csv to json operations efficiently.", "shortDescription": "Csv To Json tool for csv operations.", "name": "Csv To Json"}, "csv-to-tsv": {"description": "Csv To Tsv tool for csv operations. Perform csv to tsv operations efficiently.", "shortDescription": "Csv To Tsv tool for csv operations.", "name": "Csv To Tsv"}, "csv-to-xml": {"description": "Csv To Xml tool for csv operations. Perform csv to xml operations efficiently.", "shortDescription": "Csv To Xml tool for csv operations.", "name": "Csv To Xml"}, "csv-to-yaml": {"description": "Csv To Yaml tool for csv operations. Perform csv to yaml operations efficiently.", "shortDescription": "Csv To Yaml tool for csv operations.", "name": "Csv To Yaml"}, "find-incomplete-csv-records": {"description": "Найти Incomplete Csv Records tool for csv operations. Perform find incomplete csv records operations efficiently.", "shortDescription": "Найти Incomplete Csv Records tool for csv operations.", "name": "Найти Incomplete Csv Records"}, "insert-csv-columns": {"description": "Insert Csv Columns tool for csv operations. Perform insert csv columns operations efficiently.", "shortDescription": "Insert Csv Columns tool for csv operations.", "name": "Insert Csv Columns"}, "swap-csv-columns": {"description": "Swap Csv Columns tool for csv operations. Perform swap csv columns operations efficiently.", "shortDescription": "Swap Csv Columns tool for csv operations.", "name": "Swap Csv Columns"}, "transpose-csv": {"description": "Transpose Csv tool for csv operations. Perform transpose csv operations efficiently.", "shortDescription": "Transpose Csv tool for csv operations.", "name": "Transpose Csv"}}, "image-generic": {"image-to-text": {"name": "Image to Text (OCR)", "description": "Извлечь text from images using OCR", "shortDescription": "Извлечь text from images"}, "resize": {"name": "Изменить размер Image", "description": "Изменить размер images to specific dimensions", "shortDescription": "Изменить размер image dimensions"}, "compress": {"name": "Сжать Image", "description": "Reduce image file size while maintaining quality", "shortDescription": "Сжать image file size"}, "remove-background": {"name": "Удалить Background", "description": "Удалить background from images automatically", "shortDescription": "Удалить image background"}, "crop": {"name": "Crop Image", "description": "Crop images to specific areas", "shortDescription": "Crop image area"}, "change-opacity": {"name": "Change Opacity", "description": "Adjust image transparency/opacity", "shortDescription": "Change image opacity"}, "change-colors": {"name": "Change Colors", "description": "Modify colors in images", "shortDescription": "Change image colors"}, "create-transparent": {"name": "Create Transparent", "description": "Make specific colors transparent in images", "shortDescription": "Create transparent image"}, "qr-code": {"name": "QR Code Generator", "description": "Генерировать QR codes for different data types", "shortDescription": "Генерировать QR codes"}}, "number": {"sum": {"name": "Sum Numbers", "description": "Sum Numbers tool for number operations. Perform sum operations efficiently.", "shortDescription": "Sum Numbers tool for number operations."}, "generate": {"name": "Генерировать Numbers", "description": "Генерировать Numbers tool for number operations. Perform generate operations efficiently.", "shortDescription": "Генерировать Numbers tool for number operations."}, "arithmetic-sequence": {"description": "Arithmetic Sequence tool for number operations. Perform arithmetic sequence operations efficiently.", "shortDescription": "Arithmetic Sequence tool for number operations.", "name": "Arithmetic Sequence"}, "generic-calc": {"name": "Generic <PERSON>", "description": "Generic Calc tool for number operations. Perform generic calc operations efficiently.", "shortDescription": "Generic Calc tool for number operations."}}, "list": {"sort": {"name": "Сортировать List", "description": "Сортировать List tool for list operations. Perform sort operations efficiently.", "shortDescription": "Сортировать List tool for list operations."}, "shuffle": {"name": "Shuffle List", "description": "Shuffle List tool for list operations. Perform shuffle operations efficiently.", "shortDescription": "Shuffle List tool for list operations."}, "find-unique": {"name": "Найти Unique Items", "description": "Найти Unique Items tool for list operations. Perform find unique operations efficiently.", "shortDescription": "Найти Unique Items tool for list operations."}, "group": {"name": "Group List", "description": "Group List tool for list operations. Perform group operations efficiently.", "shortDescription": "Group List tool for list operations."}, "unwrap": {"name": "Unwrap List", "description": "Unwrap List tool for list operations. Perform unwrap operations efficiently.", "shortDescription": "Unwrap List tool for list operations."}, "rotate": {"name": "Rotate List", "description": "Rotate List tool for list operations. Perform rotate operations efficiently.", "shortDescription": "Rotate List tool for list operations."}, "duplicate": {"description": "Duplicate tool for list operations. Perform duplicate operations efficiently.", "shortDescription": "Duplicate tool for list operations.", "name": "Duplicate"}, "find-most-popular": {"description": "Найти Most Popular tool for list operations. Perform find most popular operations efficiently.", "shortDescription": "Найти Most Popular tool for list operations.", "name": "Найти Most Popular"}, "reverse": {"description": "Обратить tool for list operations. Perform reverse operations efficiently.", "shortDescription": "Обратить tool for list operations.", "name": "Обратить"}, "truncate": {"description": "Truncate tool for list operations. Perform truncate operations efficiently.", "shortDescription": "Truncate tool for list operations.", "name": "Truncate"}, "wrap": {"description": "Wrap tool for list operations. Perform wrap operations efficiently.", "shortDescription": "Wrap tool for list operations.", "name": "Wrap"}}, "json": {"prettify": {"name": "Prettify JSON", "description": "Prettify JSON tool for json operations. Perform prettify operations efficiently.", "shortDescription": "Prettify JSON tool for json operations."}, "escape-json": {"description": "Escape Json tool for json operations. Perform escape json operations efficiently.", "shortDescription": "Escape Json tool for json operations.", "name": "Escape Json"}, "json-to-xml": {"description": "Преобразовать JSON data to XML format with customizable options.", "shortDescription": "Преобразовать JSON data to XML format with customizable options.", "name": "Json To Xml"}, "minify": {"description": "Minify tool for json operations. Perform minify operations efficiently.", "shortDescription": "Minify tool for json operations.", "name": "Minify"}, "stringify": {"description": "Stringify tool for json operations. Perform stringify operations efficiently.", "shortDescription": "Stringify tool for json operations.", "name": "Stringify"}, "tsv-to-json": {"description": "Tsv To Json tool for json operations. Perform tsv to json operations efficiently.", "shortDescription": "Tsv To Json tool for json operations.", "name": "Tsv To Json"}, "validateJson": {"description": "ПроверитьJson tool for json operations. Perform validateJson operations efficiently.", "shortDescription": "ПроверитьJson tool for json operations.", "name": "Провери<PERSON><PERSON><PERSON><PERSON>"}}, "pdf": {"split-pdf": {"name": "Split PDF", "description": "Split PDF tool for pdf operations. Perform split pdf operations efficiently.", "shortDescription": "Split PDF tool for pdf operations."}, "merge-pdf": {"name": "Merge PDF", "description": "Merge PDF tool for pdf operations. Perform merge pdf operations efficiently.", "shortDescription": "Merge PDF tool for pdf operations."}, "rotate-pdf": {"name": "Rotate PDF", "description": "Rotate PDF tool for pdf operations. Perform rotate pdf operations efficiently.", "shortDescription": "Rotate PDF tool for pdf operations."}, "compress-pdf": {"name": "Сжать PDF", "description": "Сжать PDF tool for pdf operations. Perform compress pdf operations efficiently.", "shortDescription": "Сжать PDF tool for pdf operations."}, "protect-pdf": {"name": "Protect PDF", "description": "Protect PDF tool for pdf operations. Perform protect pdf operations efficiently.", "shortDescription": "Protect PDF tool for pdf operations."}, "pdf-to-epub": {"description": "Pdf To Epub tool for pdf operations. Perform pdf to epub operations efficiently.", "shortDescription": "Pdf To Epub tool for pdf operations.", "name": "Pdf To Epub"}}, "time": {"time-between-dates": {"name": "Time Between Dates", "description": "Time Between Dates tool for time operations. Perform time between dates operations efficiently.", "shortDescription": "Time Between Dates tool for time operations."}, "convert-days-to-hours": {"name": "Days to Hours", "description": "Days to Hours tool for time operations. Perform convert days to hours operations efficiently.", "shortDescription": "Days to Hours tool for time operations."}, "convert-hours-to-days": {"name": "Hours to Days", "description": "Hours to Days tool for time operations. Perform convert hours to days operations efficiently.", "shortDescription": "Hours to Days tool for time operations."}, "convert-seconds-to-time": {"name": "Seconds to Time", "description": "Seconds to Time tool for time operations. Perform convert seconds to time operations efficiently.", "shortDescription": "Seconds to Time tool for time operations."}, "convert-time-to-seconds": {"name": "Time to Seconds", "description": "Time to Seconds tool for time operations. Perform convert time to seconds operations efficiently.", "shortDescription": "Time to Seconds tool for time operations."}, "truncate-clock-time": {"name": "Truncate Clock Time", "description": "Truncate Clock Time tool for time operations. Perform truncate clock time operations efficiently.", "shortDescription": "Truncate Clock Time tool for time operations."}}, "video": {"trim": {"name": "Trim Video", "description": "Trim Video tool for video operations. Perform trim operations efficiently.", "shortDescription": "Trim Video tool for video operations."}, "change-speed": {"description": "Change Speed tool for video operations. Perform change speed operations efficiently.", "shortDescription": "Change Speed tool for video operations.", "name": "Change Speed"}, "compress": {"description": "Сжать tool for video operations. Perform compress operations efficiently.", "shortDescription": "Сжать tool for video operations.", "name": "Сжать"}, "crop-video": {"description": "Crop Video tool for video operations. Perform crop video operations efficiently.", "shortDescription": "Crop Video tool for video operations.", "name": "Crop Video"}, "flip": {"description": "Flip tool for video operations. Perform flip operations efficiently.", "shortDescription": "Flip tool for video operations.", "name": "Flip"}, "gif": {"change-speed": {"description": "Change Speed tool for video operations. Perform change speed operations efficiently.", "shortDescription": "Change Speed tool for video operations.", "name": "Change Speed"}}, "loop": {"description": "Loop tool for video operations. Perform loop operations efficiently.", "shortDescription": "Loop tool for video operations.", "name": "Loop"}, "rotate": {"description": "Rotate tool for video operations. Perform rotate operations efficiently.", "shortDescription": "Rotate tool for video operations.", "name": "Rotate"}}, "gif": {"change-speed": {"name": "Change GIF Speed", "description": "Change the speed of GIF animations", "shortDescription": "Change GIF speed"}}, "png": {"compress-png": {"name": "Сжать PNG", "description": "Сжать PNG images to reduce file size", "shortDescription": "Сжать PNG files"}, "convert-jgp-to-png": {"name": "Преобразовать JPG to PNG", "description": "Преобразовать JPG images to PNG format", "shortDescription": "Преобразовать JPG to PNG"}}, "image": {"generic": {"change-colors": {"description": "Change Colors tool for image operations. Perform change colors operations efficiently.", "shortDescription": "Change Colors tool for image operations.", "name": "Change Colors"}, "change-opacity": {"description": "Change Opacity tool for image operations. Perform change opacity operations efficiently.", "shortDescription": "Change Opacity tool for image operations.", "name": "Change Opacity"}, "compress": {"description": "Сжать images to reduce file size while maintaining quality.", "shortDescription": "Сжать images to reduce file size while maintaining quality.", "name": "Сжать"}, "create-transparent": {"description": "Create Transparent tool for image operations. Perform create transparent operations efficiently.", "shortDescription": "Create Transparent tool for image operations.", "name": "Create Transparent"}, "crop": {"description": "Crop images to remove unwanted areas.", "shortDescription": "Crop images to remove unwanted areas.", "name": "Crop"}, "image-to-text": {"description": "Извлечь text from images using OCR technology.", "shortDescription": "Извлечь text from images using OCR technology.", "name": "Image To Text"}, "qr-code": {"description": "Генерировать QR codes from text or URLs.", "shortDescription": "Генерировать QR codes from text or URLs.", "name": "Qr Code"}, "remove-background": {"description": "Удалить background from images automatically.", "shortDescription": "Удалить background from images automatically.", "name": "Удалить Background"}, "resize": {"description": "Изменить размер images to specific dimensions.", "shortDescription": "Изменить размер images to specific dimensions.", "name": "Изменить размер"}}, "png": {"compress-png": {"description": "Сжать Png tool for image operations. Perform compress png operations efficiently.", "shortDescription": "Сжать Png tool for image operations.", "name": "Сжать Png"}, "convert-jgp-to-png": {"description": "Преобразовать Jgp To Png tool for image operations. Perform convert jgp to png operations efficiently.", "shortDescription": "Преобразовать Jgp To Png tool for image operations.", "name": "Преобразовать Jgp To Png"}}}}