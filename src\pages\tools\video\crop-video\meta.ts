import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Crop Video',
  path: 'crop-video',
  icon: 'mdi:tools',
  description: 'Crop Video tool for video operations. Perform crop video operations efficiently.',
  shortDescription: 'Crop Video tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
