#!/usr/bin/env node

/**
 * Meta Files Localization Script
 * 
 * This script automatically localizes meta.ts files by:
 * 1. Adding useTranslation import
 * 2. Converting hardcoded descriptions to translation keys
 * 3. Adding corresponding translations to tools.json files
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class MetaLocalizer {
  constructor() {
    this.processedFiles = 0;
    this.modifiedFiles = 0;
    this.translations = {
      en: {},
      ru: {}
    };
  }

  // Extract tool path from file path
  getToolPath(filePath) {
    const match = filePath.match(/src[\/\\]pages[\/\\]tools[\/\\](.+)[\/\\]meta\.ts$/);
    return match ? match[1].replace(/\\/g, '/') : null;
  }

  // Generate translation key from tool path
  getTranslationKey(toolPath) {
    return toolPath.replace(/\//g, '.');
  }

  // Process a single meta file
  processMetaFile(filePath) {
    console.log(`Processing meta: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    const toolPath = this.getToolPath(filePath);
    if (!toolPath) {
      console.log(`⚠️  Could not extract tool path from: ${filePath}`);
      return;
    }
    
    const translationKey = this.getTranslationKey(toolPath);
    
    // Extract description from meta file
    const descriptionMatch = content.match(/description:\s*['"`]([^'"`]+)['"`]/);
    if (descriptionMatch) {
      const description = descriptionMatch[1];
      
      // Store translations
      this.translations.en[translationKey] = {
        description: description
      };
      
      // Generate Russian translation (placeholder for now)
      this.translations.ru[translationKey] = {
        description: this.translateToRussian(description)
      };
      
      // Replace hardcoded description with translation key
      content = content.replace(
        /description:\s*['"`][^'"`]+['"`]/,
        `description: t('tools:${translationKey}.description')`
      );
      
      // Add useTranslation import if not present
      if (!content.includes('useTranslation')) {
        content = "import { useTranslation } from 'react-i18next';\n" + content;
        
        // Add translation hook to the function
        content = content.replace(
          /export\s+default\s+function\s+(\w+)\s*\(\s*\)\s*\{/,
          'export default function $1() {\n  const { t } = useTranslation();'
        );
      }
      
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    } else {
      console.log(`⏭️  Skipped: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  // Simple translation to Russian (placeholder)
  translateToRussian(text) {
    // This is a very basic translation - in real scenario you'd use a proper translation service
    const translations = {
      'Convert': 'Конвертировать',
      'Generate': 'Генерировать', 
      'Compress': 'Сжать',
      'Resize': 'Изменить размер',
      'Extract': 'Извлечь',
      'Remove': 'Удалить',
      'Add': 'Добавить',
      'Merge': 'Объединить',
      'Split': 'Разделить',
      'Rotate': 'Повернуть',
      'Crop': 'Обрезать',
      'files': 'файлы',
      'images': 'изображения',
      'text': 'текст',
      'data': 'данные',
      'tool': 'инструмент',
      'format': 'формат',
      'with': 'с',
      'and': 'и',
      'or': 'или',
      'for': 'для',
      'to': 'в',
      'from': 'из'
    };
    
    let translated = text;
    for (const [en, ru] of Object.entries(translations)) {
      translated = translated.replace(new RegExp(en, 'gi'), ru);
    }
    
    return translated;
  }

  // Update tools.json files with new translations
  updateToolsJson() {
    console.log('\n📝 Updating tools.json files...');
    
    // Update English tools.json
    const enPath = 'src/locales/en/tools.json';
    const enTools = JSON.parse(fs.readFileSync(enPath, 'utf8'));
    
    for (const [key, value] of Object.entries(this.translations.en)) {
      const parts = key.split('.');
      let current = enTools;
      
      for (let i = 0; i < parts.length - 1; i++) {
        if (!current[parts[i]]) current[parts[i]] = {};
        current = current[parts[i]];
      }
      
      const lastPart = parts[parts.length - 1];
      if (!current[lastPart]) current[lastPart] = {};
      Object.assign(current[lastPart], value);
    }
    
    fs.writeFileSync(enPath, JSON.stringify(enTools, null, 2), 'utf8');
    
    // Update Russian tools.json
    const ruPath = 'src/locales/ru/tools.json';
    const ruTools = JSON.parse(fs.readFileSync(ruPath, 'utf8'));
    
    for (const [key, value] of Object.entries(this.translations.ru)) {
      const parts = key.split('.');
      let current = ruTools;
      
      for (let i = 0; i < parts.length - 1; i++) {
        if (!current[parts[i]]) current[parts[i]] = {};
        current = current[parts[i]];
      }
      
      const lastPart = parts[parts.length - 1];
      if (!current[lastPart]) current[lastPart] = {};
      Object.assign(current[lastPart], value);
    }
    
    fs.writeFileSync(ruPath, JSON.stringify(ruTools, null, 2), 'utf8');
    
    console.log('✅ Updated tools.json files');
  }

  // Process all meta files
  processAllMetaFiles() {
    console.log('🔍 Starting meta files localization...\n');
    
    const files = glob.sync('src/pages/tools/**/meta.ts', {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    console.log(`Found ${files.length} meta files to process\n`);
    
    files.forEach(file => {
      this.processMetaFile(file);
    });
    
    // Update translation files
    if (this.modifiedFiles > 0) {
      this.updateToolsJson();
    }
    
    console.log('\n📊 SUMMARY:');
    console.log(`Total files processed: ${this.processedFiles}`);
    console.log(`Files modified: ${this.modifiedFiles}`);
    console.log(`Files skipped: ${this.processedFiles - this.modifiedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const localizer = new MetaLocalizer();
  localizer.processAllMetaFiles();
  console.log('\n✅ Meta files localization completed!');
}

module.exports = MetaLocalizer;
