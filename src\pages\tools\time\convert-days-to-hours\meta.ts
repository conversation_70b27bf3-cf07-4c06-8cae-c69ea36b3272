import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Days to Hours',
  path: 'convert-days-to-hours',
  icon: 'mdi:tools',
  description: 'With this browser-based application, you can calculate how many hours there are in the given number of days. The application takes the input values (days), multiplies them by 24 and that converts them into hours. It supports both integer and decimal day values and it can convert multiple values at the same time.',
  shortDescription: 'Convert days to hours easily.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
