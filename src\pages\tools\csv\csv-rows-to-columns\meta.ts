import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Csv Rows To Columns',
  path: 'csv-rows-to-columns',
  icon: 'mdi:tools',
  description: 'Csv Rows To Columns tool for csv operations. Perform csv rows to columns operations efficiently.',
  shortDescription: 'Csv Rows To Columns tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
