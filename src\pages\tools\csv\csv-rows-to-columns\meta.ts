import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Tool',
  path: 'csv-rows-to-columns',
  icon: 'mdi:tools',
  description: 'This tool converts rows of a CSV (Comma Separated Values) file into columns. It extracts the horizontal lines from the input CSV one by one, rotates them 90 degrees, and outputs them as vertical columns one after another, separated by commas.',
  shortDescription: 'Convert CSV rows to columns.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
