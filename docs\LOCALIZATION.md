# 🌍 Localization Guide for OmniTools

This guide explains how to add and maintain translations in the OmniTools project.

## 📁 Project Structure

```
src/
├── locales/
│   ├── en/
│   │   ├── common.json    # Common UI elements
│   │   └── tools.json     # Tool-specific translations
│   └── ru/
│       ├── common.json    # Russian translations
│       └── tools.json     # Russian tool translations
├── i18n/
│   └── index.ts          # i18n configuration
└── hooks/
    └── useToolTranslation.ts  # Translation hooks
```

## 🚀 Quick Start

### Adding a New Language

1. Create a new folder in `src/locales/` with the language code (e.g., `fr` for French)
2. Copy the structure from `en/` folder
3. Translate all strings in the JSON files
4. Update `src/i18n/index.ts` to include the new language
5. Add the language to `supportedLanguages` array

### Using Translations in Components

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('common.title')}</h1>
      <p>{t('tools:string.base64.description')}</p>
    </div>
  );
}
```

### Translation Key Naming Convention

- **Common UI elements**: `common.keyName`
- **Tool-specific**: `tools:category.toolName.keyName`
- **Nested objects**: Use dot notation (`common.navigation.home`)

## 📝 Translation Files

### common.json Structure

```json
{
  "navigation": {
    "home": "Home",
    "categories": "Categories"
  },
  "common": {
    "loading": "Loading...",
    "save": "Save",
    "cancel": "Cancel"
  },
  "errors": {
    "fileNotSupported": "File type not supported"
  }
}
```

### tools.json Structure

```json
{
  "string": {
    "base64": {
      "name": "Base64 Encode/Decode",
      "description": "Encode or decode text using Base64",
      "optionsTitle": "Base64 Options"
    }
  }
}
```

## 🔧 Development Tools

### Check Localization Status

```bash
npm run check:localization
```

This script will:
- Find hardcoded English strings
- Check for missing translations
- Identify components not using `useTranslation`
- Generate a detailed report

### Example Output

```
📊 LOCALIZATION REPORT
==================================================

📈 STATISTICS:
Total files checked: 145
Files using translation: 23 (16%)
Files with suspicious strings: 67
Clean localized files: 8

⚠️  ISSUES FOUND:
❌ Missing translations in ru: tools.string.newTool.name, tools.string.newTool.description
⚠️  File contains hardcoded strings but doesn't use useTranslation: "Loading", "Search"
   📁 src/components/SomeComponent.tsx
```

## 📋 Localization Checklist

When adding a new tool or component:

- [ ] Add `useTranslation` hook
- [ ] Replace all hardcoded strings with `t()` calls
- [ ] Add translation keys to both `en/` and `ru/` files
- [ ] Test with language switcher
- [ ] Run `npm run check:localization`
- [ ] Update this documentation if needed

## 🎯 Best Practices

### 1. Use Descriptive Keys

```tsx
// ❌ Bad
t('text1')

// ✅ Good  
t('common.saveButton')
t('tools:string.base64.encodeOption')
```

### 2. Handle Pluralization

```json
{
  "itemCount": "{{count}} item",
  "itemCount_plural": "{{count}} items"
}
```

```tsx
t('itemCount', { count: items.length })
```

### 3. Use Interpolation for Dynamic Content

```json
{
  "welcome": "Welcome, {{name}}!",
  "fileSize": "File size: {{size}} MB"
}
```

```tsx
t('welcome', { name: user.name })
t('fileSize', { size: file.size })
```

### 4. Organize by Context

Group related translations together:

```json
{
  "fileUpload": {
    "title": "Upload File",
    "dragDrop": "Drag and drop file here",
    "browse": "Browse files",
    "maxSize": "Max size: {{size}} MB"
  }
}
```

## 🌐 Supported Languages

Currently supported languages:

- 🇺🇸 **English** (`en`) - Base language
- 🇷🇺 **Russian** (`ru`) - Complete translation

### Adding More Languages

To add a new language:

1. **Create locale files**:
   ```bash
   mkdir src/locales/fr
   cp src/locales/en/* src/locales/fr/
   ```

2. **Update i18n config**:
   ```typescript
   // src/i18n/index.ts
   import frCommon from '../locales/fr/common.json';
   import frTools from '../locales/fr/tools.json';

   const resources = {
     en: { common: enCommon, tools: enTools },
     ru: { common: ruCommon, tools: ruTools },
     fr: { common: frCommon, tools: frTools }  // Add this
   };

   export const supportedLanguages = [
     { code: 'en', name: 'English', nativeName: 'English' },
     { code: 'ru', name: 'Russian', nativeName: 'Русский' },
     { code: 'fr', name: 'French', nativeName: 'Français' }  // Add this
   ];
   ```

3. **Translate all strings** in the new locale files

4. **Test thoroughly** with the language switcher

## 🔍 Troubleshooting

### Common Issues

1. **Missing translation key**:
   ```
   Error: Missing key "tools:string.newTool.name"
   ```
   Solution: Add the key to all locale files

2. **Hardcoded strings**:
   ```
   Warning: File contains hardcoded strings: "Loading", "Save"
   ```
   Solution: Replace with `t('common.loading')`, `t('common.save')`

3. **Namespace not found**:
   ```
   Error: Namespace "tools" not loaded
   ```
   Solution: Check i18n configuration and imports

### Debug Mode

Enable debug mode to see missing keys:

```typescript
// src/i18n/index.ts
i18n.init({
  debug: process.env.NODE_ENV === 'development',
  // ... other options
});
```

## 📊 Current Status

**Localization Progress**: ~25% complete

### Completed ✅
- Core navigation components
- Loading states
- File input components
- Tool options framework
- Popular string tools (base64, uppercase, to-morse)

### In Progress 🚧
- Remaining string tools
- Image processing tools
- PDF tools
- Error messages

### Todo 📝
- CSV tools
- JSON tools
- Video/Audio tools
- Advanced features

## 🤝 Contributing

When contributing translations:

1. Follow the existing key naming conventions
2. Keep translations concise but descriptive
3. Test with actual UI components
4. Run the localization checker
5. Update documentation if adding new patterns

For questions or help with translations, please open an issue or contact the maintainers.
