import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Compress',
  path: 'compress',
  icon: 'mdi:tools',
  description: 'Compress images to reduce file size while maintaining quality.',
  shortDescription: 'Compress images to reduce file size while maintaining quality.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
