import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Tool',
  path: 'compress',
  icon: 'mdi:tools',
  description: 'Compress images to reduce file size while maintaining reasonable quality.',
  shortDescription: 'Compress images to reduce file size while maintaining reasonable quality.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
