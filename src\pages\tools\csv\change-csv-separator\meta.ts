import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Change Csv Separator',
  path: 'change-csv-separator',
  icon: 'mdi:tools',
  description: 'Change Csv Separator tool for csv operations. Perform change csv separator operations efficiently.',
  shortDescription: 'Change Csv Separator tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
