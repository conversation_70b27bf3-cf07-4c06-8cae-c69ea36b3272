Index: .idea/workspace.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project version=\"4\">\r\n  <component name=\"AutoImportSettings\">\r\n    <option name=\"autoReloadType\" value=\"SELECTIVE\" />\r\n  </component>\r\n  <component name=\"ChangeListManager\">\r\n    <list default=\"true\" id=\"b30e2810-c4c1-4aad-b134-794e52cc1c7d\" name=\"Changes\" comment=\"refact: examples\">\r\n      <change beforePath=\"$PROJECT_DIR$/.idea/workspace.xml\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/.idea/workspace.xml\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/src/components/ToolHeader.tsx\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/src/components/ToolHeader.tsx\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/src/components/examples/Examples.tsx\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/src/components/examples/ToolExamples.tsx\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/src/pages/tools/string/join/index.tsx\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/src/pages/tools/string/join/index.tsx\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/src/pages/tools/string/split/index.tsx\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/src/pages/tools/string/split/index.tsx\" afterDir=\"false\" />\r\n      <change beforePath=\"$PROJECT_DIR$/src/tools/defineTool.tsx\" beforeDir=\"false\" afterPath=\"$PROJECT_DIR$/src/tools/defineTool.tsx\" afterDir=\"false\" />\r\n    </list>\r\n    <option name=\"SHOW_DIALOG\" value=\"false\" />\r\n    <option name=\"HIGHLIGHT_CONFLICTS\" value=\"true\" />\r\n    <option name=\"HIGHLIGHT_NON_ACTIVE_CHANGELIST\" value=\"false\" />\r\n    <option name=\"LAST_RESOLUTION\" value=\"IGNORE\" />\r\n  </component>\r\n  <component name=\"FormatOnSaveOptions\">\r\n    <option name=\"myRunOnSave\" value=\"true\" />\r\n  </component>\r\n  <component name=\"Git.Merge.Settings\">\r\n    <option name=\"BRANCH\" value=\"origin/main\" />\r\n  </component>\r\n  <component name=\"Git.Settings\">\r\n    <option name=\"RECENT_BRANCH_BY_REPOSITORY\">\r\n      <map>\r\n        <entry key=\"$PROJECT_DIR$\" value=\"main\" />\r\n      </map>\r\n    </option>\r\n    <option name=\"RECENT_GIT_ROOT_PATH\" value=\"$PROJECT_DIR$\" />\r\n    <option name=\"RESET_MODE\" value=\"HARD\" />\r\n  </component>\r\n  <component name=\"GitHubPullRequestSearchHistory\">{\r\n  &quot;lastFilter&quot;: {\r\n    &quot;state&quot;: &quot;OPEN&quot;,\r\n    &quot;assignee&quot;: &quot;iib0011&quot;\r\n  }\r\n}</component>\r\n  <component name=\"GithubPullRequestsUISettings\">{\r\n  &quot;selectedUrlAndAccountId&quot;: {\r\n    &quot;url&quot;: &quot;https://github.com/iib0011/omni-tools.git&quot;,\r\n    &quot;accountId&quot;: &quot;45f8cd51-000f-4ba4-a4c6-c4d96ac9b1e5&quot;\r\n  }\r\n}</component>\r\n  <component name=\"KubernetesApiProvider\">{\r\n  &quot;isMigrated&quot;: true\r\n}</component>\r\n  <component name=\"MarkdownSettingsMigration\">\r\n    <option name=\"stateVersion\" value=\"1\" />\r\n  </component>\r\n  <component name=\"ProjectColorInfo\">{\r\n  &quot;associatedIndex&quot;: 0\r\n}</component>\r\n  <component name=\"ProjectId\" id=\"2i6g3WkUxdCURKYvUIJ9LMY5Qsc\" />\r\n  <component name=\"ProjectLevelVcsManager\" settingsEditedManually=\"true\">\r\n    <ConfirmationsSetting value=\"2\" id=\"Add\" />\r\n  </component>\r\n  <component name=\"ProjectViewState\">\r\n    <option name=\"autoscrollFromSource\" value=\"true\" />\r\n    <option name=\"hideEmptyMiddlePackages\" value=\"true\" />\r\n    <option name=\"showLibraryContents\" value=\"true\" />\r\n  </component>\r\n  <component name=\"PropertiesComponent\"><![CDATA[{\r\n  \"keyToString\": {\r\n    \"ASKED_ADD_EXTERNAL_FILES\": \"true\",\r\n    \"ASKED_SHARE_PROJECT_CONFIGURATION_FILES\": \"true\",\r\n    \"Docker.Dockerfile build.executor\": \"Run\",\r\n    \"Docker.Dockerfile.executor\": \"Run\",\r\n    \"Playwright.JoinText Component.executor\": \"Run\",\r\n    \"Playwright.JoinText Component.should merge text pieces with specified join character.executor\": \"Run\",\r\n    \"RunOnceActivity.OpenProjectViewOnStart\": \"true\",\r\n    \"RunOnceActivity.ShowReadmeOnStart\": \"true\",\r\n    \"RunOnceActivity.git.unshallow\": \"true\",\r\n    \"Vitest.compute function (1).executor\": \"Run\",\r\n    \"Vitest.compute function.executor\": \"Run\",\r\n    \"Vitest.mergeText.executor\": \"Run\",\r\n    \"Vitest.mergeText.should merge lines and preserve blank lines when deleteBlankLines is false.executor\": \"Run\",\r\n    \"Vitest.mergeText.should merge lines, preserve blank lines and trailing spaces when both deleteBlankLines and deleteTrailingSpaces are false.executor\": \"Run\",\r\n    \"git-widget-placeholder\": \"examples\",\r\n    \"ignore.virus.scanning.warn.message\": \"true\",\r\n    \"kotlin-language-version-configured\": \"true\",\r\n    \"last_opened_file_path\": \"C:/Users/<USER>/IdeaProjects/omni-tools/src/assets\",\r\n    \"node.js.detected.package.eslint\": \"true\",\r\n    \"node.js.detected.package.tslint\": \"true\",\r\n    \"node.js.selected.package.eslint\": \"(autodetect)\",\r\n    \"node.js.selected.package.tslint\": \"(autodetect)\",\r\n    \"nodejs_package_manager_path\": \"npm\",\r\n    \"npm.dev.executor\": \"Run\",\r\n    \"npm.lint.executor\": \"Run\",\r\n    \"npm.prebuild.executor\": \"Run\",\r\n    \"npm.script:create:tool.executor\": \"Run\",\r\n    \"npm.test.executor\": \"Run\",\r\n    \"npm.test:e2e.executor\": \"Run\",\r\n    \"npm.test:e2e:run.executor\": \"Run\",\r\n    \"prettierjs.PrettierConfiguration.Package\": \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\omni-tools\\\\node_modules\\\\prettier\",\r\n    \"project.structure.last.edited\": \"Problems\",\r\n    \"project.structure.proportion\": \"0.0\",\r\n    \"project.structure.side.proportion\": \"0.2\",\r\n    \"settings.editor.selected.configurable\": \"settings.typescriptcompiler\",\r\n    \"ts.external.directory.path\": \"C:\\\\Users\\\\<USER>\\\\IdeaProjects\\\\omni-tools\\\\node_modules\\\\typescript\\\\lib\",\r\n    \"vue.rearranger.settings.migration\": \"true\"\r\n  }\r\n}]]></component>\r\n  <component name=\"ReactDesignerToolWindowState\">\r\n    <option name=\"myId2Visible\">\r\n      <map>\r\n        <entry key=\"com.intellij.reactbuddy.reactComponents\" value=\"false\" />\r\n        <entry key=\"com.intellij.reactbuddy.reactInspector\" value=\"false\" />\r\n        <entry key=\"com.intellij.reactbuddy.storybook\" value=\"false\" />\r\n      </map>\r\n    </option>\r\n  </component>\r\n  <component name=\"RecentsManager\">\r\n    <key name=\"CopyFile.RECENT_KEYS\">\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\assets\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\.github\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\components\\options\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\assets\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\pages\\string\" />\r\n    </key>\r\n    <key name=\"MoveFile.RECENT_KEYS\">\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\public\\assets\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\pages\\tools\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\pages\\categories\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\components\" />\r\n      <recent name=\"C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\src\\components\\options\" />\r\n    </key>\r\n  </component>\r\n  <component name=\"RunManager\" selected=\"npm.dev\">\r\n    <configuration name=\"Dockerfile build\" type=\"docker-deploy\" factoryName=\"dockerfile\" temporary=\"true\" server-name=\"Docker\">\r\n      <deployment type=\"dockerfile\">\r\n        <settings>\r\n          <option name=\"imageTag\" value=\"omnitools\" />\r\n          <option name=\"buildCliOptions\" value=\"--target build\" />\r\n          <option name=\"containerName\" value=\"omni-tools\" />\r\n          <option name=\"publishAllPorts\" value=\"true\" />\r\n          <option name=\"sourceFilePath\" value=\"Dockerfile\" />\r\n        </settings>\r\n      </deployment>\r\n      <method v=\"2\" />\r\n    </configuration>\r\n    <configuration name=\"Dockerfile\" type=\"docker-deploy\" factoryName=\"dockerfile\" temporary=\"true\" server-name=\"Docker\">\r\n      <deployment type=\"dockerfile\">\r\n        <settings>\r\n          <option name=\"sourceFilePath\" value=\"Dockerfile\" />\r\n        </settings>\r\n      </deployment>\r\n      <method v=\"2\" />\r\n    </configuration>\r\n    <configuration default=\"true\" type=\"docker-deploy\" factoryName=\"dockerfile\" temporary=\"true\">\r\n      <deployment type=\"dockerfile\">\r\n        <settings />\r\n      </deployment>\r\n      <method v=\"2\" />\r\n    </configuration>\r\n    <configuration name=\"dev\" type=\"js.build_tools.npm\" temporary=\"true\" nameIsGenerated=\"true\">\r\n      <package-json value=\"$PROJECT_DIR$/package.json\" />\r\n      <command value=\"run\" />\r\n      <scripts>\r\n        <script value=\"dev\" />\r\n      </scripts>\r\n      <node-interpreter value=\"project\" />\r\n      <envs />\r\n      <method v=\"2\" />\r\n    </configuration>\r\n    <configuration name=\"lint\" type=\"js.build_tools.npm\" temporary=\"true\" nameIsGenerated=\"true\">\r\n      <package-json value=\"$PROJECT_DIR$/package.json\" />\r\n      <command value=\"run\" />\r\n      <scripts>\r\n        <script value=\"lint\" />\r\n      </scripts>\r\n      <node-interpreter value=\"project\" />\r\n      <envs />\r\n      <method v=\"2\" />\r\n    </configuration>\r\n    <configuration name=\"test\" type=\"js.build_tools.npm\" temporary=\"true\" nameIsGenerated=\"true\">\r\n      <package-json value=\"$PROJECT_DIR$/package.json\" />\r\n      <command value=\"run\" />\r\n      <scripts>\r\n        <script value=\"test\" />\r\n      </scripts>\r\n      <node-interpreter value=\"project\" />\r\n      <envs />\r\n      <method v=\"2\" />\r\n    </configuration>\r\n    <list>\r\n      <item itemvalue=\"Docker.Dockerfile\" />\r\n      <item itemvalue=\"Docker.Dockerfile build\" />\r\n      <item itemvalue=\"npm.test\" />\r\n      <item itemvalue=\"npm.dev\" />\r\n      <item itemvalue=\"npm.lint\" />\r\n    </list>\r\n    <recent_temporary>\r\n      <list>\r\n        <item itemvalue=\"npm.dev\" />\r\n        <item itemvalue=\"Docker.Dockerfile\" />\r\n        <item itemvalue=\"npm.test\" />\r\n        <item itemvalue=\"npm.lint\" />\r\n        <item itemvalue=\"Docker.Dockerfile build\" />\r\n      </list>\r\n    </recent_temporary>\r\n  </component>\r\n  <component name=\"SharedIndexes\">\r\n    <attachedChunks>\r\n      <set>\r\n        <option value=\"bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.24978.46\" />\r\n        <option value=\"bundled-js-predefined-d6986cc7102b-76f8388c3a79-JavaScript-IU-243.24978.46\" />\r\n      </set>\r\n    </attachedChunks>\r\n  </component>\r\n  <component name=\"SpellCheckerSettings\" RuntimeDictionaries=\"0\" Folders=\"0\" CustomDictionaries=\"0\" DefaultDictionary=\"application-level\" UseSingleDictionary=\"true\" transferred=\"true\" />\r\n  <component name=\"TaskManager\">\r\n    <task active=\"true\" id=\"Default\" summary=\"Default task\">\r\n      <changelist id=\"b30e2810-c4c1-4aad-b134-794e52cc1c7d\" name=\"Changes\" comment=\"\" />\r\n      <created>1718816243156</created>\r\n      <option name=\"number\" value=\"Default\" />\r\n      <option name=\"presentableId\" value=\"Default\" />\r\n      <updated>1718816243156</updated>\r\n      <workItem from=\"1718816244509\" duration=\"13061000\" />\r\n      <workItem from=\"1718991004992\" duration=\"25000\" />\r\n      <workItem from=\"1718991057845\" duration=\"85000\" />\r\n      <workItem from=\"1718991144614\" duration=\"6968000\" />\r\n      <workItem from=\"1718998317252\" duration=\"8533000\" />\r\n      <workItem from=\"1719006887776\" duration=\"7000\" />\r\n      <workItem from=\"1719006951159\" duration=\"2377000\" />\r\n      <workItem from=\"1719021128819\" duration=\"3239000\" />\r\n      <workItem from=\"1719083989394\" duration=\"7971000\" />\r\n      <workItem from=\"1719092003308\" duration=\"14856000\" />\r\n      <workItem from=\"1719164664347\" duration=\"2033000\" />\r\n      <workItem from=\"1719166718305\" duration=\"1783000\" />\r\n      <workItem from=\"1719168519203\" duration=\"17675000\" />\r\n      <workItem from=\"1719197816332\" duration=\"1453000\" />\r\n      <workItem from=\"1719273044735\" duration=\"9847000\" />\r\n      <workItem from=\"1719294110005\" duration=\"3842000\" />\r\n      <workItem from=\"1719339559458\" duration=\"303000\" />\r\n      <workItem from=\"1719340295244\" duration=\"772000\" />\r\n      <workItem from=\"1719363272227\" duration=\"390000\" />\r\n      <workItem from=\"1719379971872\" duration=\"8943000\" />\r\n      <workItem from=\"1719464673797\" duration=\"38000\" />\r\n      <workItem from=\"1719475764139\" duration=\"14903000\" />\r\n      <workItem from=\"1719492452780\" duration=\"8000\" />\r\n      <workItem from=\"1719496624579\" duration=\"6148000\" />\r\n      <workItem from=\"1720542757452\" duration=\"5355000\" />\r\n      <workItem from=\"1720557527691\" duration=\"3245000\" />\r\n      <workItem from=\"1720564427492\" duration=\"1523000\" />\r\n      <workItem from=\"1720613598176\" duration=\"8000\" />\r\n      <workItem from=\"1720655252208\" duration=\"3975000\" />\r\n      <workItem from=\"1720661825389\" duration=\"4305000\" />\r\n      <workItem from=\"1720729165596\" duration=\"3258000\" />\r\n      <workItem from=\"1720911748039\" duration=\"331000\" />\r\n      <workItem from=\"1720912096050\" duration=\"3065000\" />\r\n      <workItem from=\"1740259920741\" duration=\"7742000\" />\r\n      <workItem from=\"1740270391152\" duration=\"690000\" />\r\n      <workItem from=\"1740274898695\" duration=\"2231000\" />\r\n      <workItem from=\"1740295530385\" duration=\"1120000\" />\r\n      <workItem from=\"1740300354462\" duration=\"1059000\" />\r\n      <workItem from=\"1740301493702\" duration=\"8924000\" />\r\n      <workItem from=\"1740318886545\" duration=\"856000\" />\r\n      <workItem from=\"1740348963270\" duration=\"388000\" />\r\n      <workItem from=\"1740399426653\" duration=\"627000\" />\r\n      <workItem from=\"1740459961271\" duration=\"66000\" />\r\n      <workItem from=\"1740460036909\" duration=\"8299000\" />\r\n      <workItem from=\"1740490890760\" duration=\"1889000\" />\r\n      <workItem from=\"1740503199053\" duration=\"4853000\" />\r\n      <workItem from=\"1740584243965\" duration=\"17000\" />\r\n      <workItem from=\"1740613094492\" duration=\"9615000\" />\r\n    </task>\r\n    <task id=\"LOCAL-00080\" summary=\"fix: ci\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719588378907</created>\r\n      <option name=\"number\" value=\"00080\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00080\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719588378907</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00081\" summary=\"fix: ci\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719588501953</created>\r\n      <option name=\"number\" value=\"00081\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00081\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719588501953</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00082\" summary=\"fix: ci\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719588854025</created>\r\n      <option name=\"number\" value=\"00082\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00082\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719588854025</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00083\" summary=\"fix: ci\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719589144843</created>\r\n      <option name=\"number\" value=\"00083\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00083\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719589144843</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00084\" summary=\"chore: jimp types\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719591262232</created>\r\n      <option name=\"number\" value=\"00084\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00084\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719591262232</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00085\" summary=\"fix: package.json\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719591700496</created>\r\n      <option name=\"number\" value=\"00085\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00085\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719591700496</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00086\" summary=\"feat: playwright report\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719600693979</created>\r\n      <option name=\"number\" value=\"00086\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00086\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719600693979</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00087\" summary=\"chore: idea config\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1719600739052</created>\r\n      <option name=\"number\" value=\"00087\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00087\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1719600739052</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00088\" summary=\"feat: sort list\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720545582958</created>\r\n      <option name=\"number\" value=\"00088\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00088\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720545582958</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00089\" summary=\"feat: find most popular ui\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720546921899</created>\r\n      <option name=\"number\" value=\"00089\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00089\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720546921899</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00090\" summary=\"fix: misc\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720558690146</created>\r\n      <option name=\"number\" value=\"00090\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00090\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720558690147</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00091\" summary=\"fix: style\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720564711406</created>\r\n      <option name=\"number\" value=\"00091\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00091\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720564711406</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00092\" summary=\"feat: find unique ui\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720565760853</created>\r\n      <option name=\"number\" value=\"00092\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00092\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720565760853</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00093\" summary=\"feat: group list ui\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720656867853</created>\r\n      <option name=\"number\" value=\"00093\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00093\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720656867853</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00094\" summary=\"feat: reverse list ui\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720658257129</created>\r\n      <option name=\"number\" value=\"00094\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00094\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720658257129</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00095\" summary=\"feat: self host\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720665220407</created>\r\n      <option name=\"number\" value=\"00095\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00095\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720665220408</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00096\" summary=\"chore: format number\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720730102816</created>\r\n      <option name=\"number\" value=\"00096\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00096\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720730102817</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00097\" summary=\"feat: rotate ui\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720913013183</created>\r\n      <option name=\"number\" value=\"00097\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00097\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720913013183</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00098\" summary=\"feat: shuffle ui\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720913810733</created>\r\n      <option name=\"number\" value=\"00098\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00098\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720913810733</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00099\" summary=\"refactor: remove validation schema\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720914492812</created>\r\n      <option name=\"number\" value=\"00099\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00099\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720914492812</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00100\" summary=\"refactor: optimize imports\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720914702655</created>\r\n      <option name=\"number\" value=\"00100\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00100\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720914702656</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00101\" summary=\"chore: use string tools\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1720914810712</created>\r\n      <option name=\"number\" value=\"00101\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00101\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1720914810713</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00102\" summary=\"fix: ctrl v\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740267666455</created>\r\n      <option name=\"number\" value=\"00102\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00102\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740267666455</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00103\" summary=\"feat: update readme\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740276092528</created>\r\n      <option name=\"number\" value=\"00103\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00103\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740276092528</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00104\" summary=\"feat: compress png\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740321721526</created>\r\n      <option name=\"number\" value=\"00104\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00104\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740321721526</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00105\" summary=\"feat: compress png\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740321912140</created>\r\n      <option name=\"number\" value=\"00105\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00105\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740321912140</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00106\" summary=\"fix: compress png\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740322444616</created>\r\n      <option name=\"number\" value=\"00106\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00106\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740322444616</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00107\" summary=\"fix: docs\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740324026721</created>\r\n      <option name=\"number\" value=\"00107\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00107\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740324026721</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00108\" summary=\"fix: docs\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740324069359</created>\r\n      <option name=\"number\" value=\"00108\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00108\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740324069359</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00109\" summary=\"fix: docs\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740324274955</created>\r\n      <option name=\"number\" value=\"00109\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00109\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740324274955</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00110\" summary=\"feat: funding\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740460017596</created>\r\n      <option name=\"number\" value=\"00110\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00110\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740460017596</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00111\" summary=\"feat: ui changes\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740464231905</created>\r\n      <option name=\"number\" value=\"00111\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00111\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740464231905</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00112\" summary=\"feat: ui changes\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740464250449</created>\r\n      <option name=\"number\" value=\"00112\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00112\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740464250449</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00113\" summary=\"fix: tsc\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740464642001</created>\r\n      <option name=\"number\" value=\"00113\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00113\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740464642001</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00114\" summary=\"fix: readme\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740468159111</created>\r\n      <option name=\"number\" value=\"00114\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00114\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740468159111</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00115\" summary=\"fix: broken links\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740488522618</created>\r\n      <option name=\"number\" value=\"00115\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00115\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740488522618</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00116\" summary=\"chore: style buttons\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740490919407</created>\r\n      <option name=\"number\" value=\"00116\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00116\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740490919407</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00117\" summary=\"chore: style\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740491274739</created>\r\n      <option name=\"number\" value=\"00117\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00117\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740491274739</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00118\" summary=\"style: background svg\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740491737480</created>\r\n      <option name=\"number\" value=\"00118\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00118\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740491737480</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00119\" summary=\"docs: img\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740503310227</created>\r\n      <option name=\"number\" value=\"00119\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00119\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740503310228</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00120\" summary=\"fix: bg\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740503419102</created>\r\n      <option name=\"number\" value=\"00120\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00120\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740503419102</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00121\" summary=\"fix: bg\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740504051051</created>\r\n      <option name=\"number\" value=\"00121\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00121\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740504051051</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00122\" summary=\"fix: bg\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740504100676</created>\r\n      <option name=\"number\" value=\"00122\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00122\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740504100676</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00123\" summary=\"fix: bg\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740505390205</created>\r\n      <option name=\"number\" value=\"00123\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00123\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740505390205</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00124\" summary=\"docs: readme\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740614012237</created>\r\n      <option name=\"number\" value=\"00124\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00124\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740614012237</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00125\" summary=\"docs: readme\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740614185980</created>\r\n      <option name=\"number\" value=\"00125\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00125\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740614185980</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00126\" summary=\"chore: handle enter press on search\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740614957672</created>\r\n      <option name=\"number\" value=\"00126\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00126\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740614957672</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00127\" summary=\"chore: show tooloptions in example\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740619610168</created>\r\n      <option name=\"number\" value=\"00127\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00127\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740619610169</updated>\r\n    </task>\r\n    <task id=\"LOCAL-00128\" summary=\"refact: examples\">\r\n      <option name=\"closed\" value=\"true\" />\r\n      <created>1740620866551</created>\r\n      <option name=\"number\" value=\"00128\" />\r\n      <option name=\"presentableId\" value=\"LOCAL-00128\" />\r\n      <option name=\"project\" value=\"LOCAL\" />\r\n      <updated>1740620866551</updated>\r\n    </task>\r\n    <option name=\"localTasksCounter\" value=\"129\" />\r\n    <servers />\r\n  </component>\r\n  <component name=\"TypeScriptGeneratedFilesManager\">\r\n    <option name=\"version\" value=\"3\" />\r\n  </component>\r\n  <component name=\"Vcs.Log.History.Properties\">\r\n    <option name=\"COLUMN_ID_ORDER\">\r\n      <list>\r\n        <option value=\"Default.Root\" />\r\n        <option value=\"Default.Author\" />\r\n        <option value=\"Default.Date\" />\r\n        <option value=\"Default.Subject\" />\r\n        <option value=\"Space.CommitStatus\" />\r\n      </list>\r\n    </option>\r\n  </component>\r\n  <component name=\"Vcs.Log.Tabs.Properties\">\r\n    <option name=\"TAB_STATES\">\r\n      <map>\r\n        <entry key=\"MAIN\">\r\n          <value>\r\n            <State />\r\n          </value>\r\n        </entry>\r\n      </map>\r\n    </option>\r\n  </component>\r\n  <component name=\"VcsManagerConfiguration\">\r\n    <option name=\"CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT\" value=\"false\" />\r\n    <option name=\"CHECK_NEW_TODO\" value=\"false\" />\r\n    <option name=\"ADD_EXTERNAL_FILES_SILENTLY\" value=\"true\" />\r\n    <MESSAGE value=\"chore: format number\" />\r\n    <MESSAGE value=\"feat: rotate ui\" />\r\n    <MESSAGE value=\"feat: shuffle ui\" />\r\n    <MESSAGE value=\"refactor: remove validation schema\" />\r\n    <MESSAGE value=\"refactor: optimize imports\" />\r\n    <MESSAGE value=\"chore: use string tools\" />\r\n    <MESSAGE value=\"fix: ctrl v\" />\r\n    <MESSAGE value=\"feat: update readme\" />\r\n    <MESSAGE value=\"feat: compress png\" />\r\n    <MESSAGE value=\"fix: compress png\" />\r\n    <MESSAGE value=\"fix: docs\" />\r\n    <MESSAGE value=\"feat: funding\" />\r\n    <MESSAGE value=\"feat: ui changes\" />\r\n    <MESSAGE value=\"fix: tsc\" />\r\n    <MESSAGE value=\"fix: readme\" />\r\n    <MESSAGE value=\"fix: broken links\" />\r\n    <MESSAGE value=\"chore: style buttons\" />\r\n    <MESSAGE value=\"chore: style\" />\r\n    <MESSAGE value=\"style: background svg\" />\r\n    <MESSAGE value=\"docs: img\" />\r\n    <MESSAGE value=\"fix: bg\" />\r\n    <MESSAGE value=\"docs: readme\" />\r\n    <MESSAGE value=\"chore: handle enter press on search\" />\r\n    <MESSAGE value=\"chore: show tooloptions in example\" />\r\n    <MESSAGE value=\"refact: examples\" />\r\n    <option name=\"LAST_COMMIT_MESSAGE\" value=\"refact: examples\" />\r\n  </component>\r\n  <component name=\"XSLT-Support.FileAssociations.UIState\">\r\n    <expand />\r\n    <select />\r\n  </component>\r\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/workspace.xml b/.idea/workspace.xml
--- a/.idea/workspace.xml	(revision a713690882b7cecea240163d06ee0b3715158faf)
+++ b/.idea/workspace.xml	(date 1740653982366)
@@ -4,14 +4,7 @@
     <option name="autoReloadType" value="SELECTIVE" />
   </component>
   <component name="ChangeListManager">
-    <list default="true" id="b30e2810-c4c1-4aad-b134-794e52cc1c7d" name="Changes" comment="refact: examples">
-      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/src/components/ToolHeader.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/ToolHeader.tsx" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/src/components/examples/Examples.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/examples/ToolExamples.tsx" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/src/pages/tools/string/join/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/tools/string/join/index.tsx" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/src/pages/tools/string/split/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/tools/string/split/index.tsx" afterDir="false" />
-      <change beforePath="$PROJECT_DIR$/src/tools/defineTool.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/tools/defineTool.tsx" afterDir="false" />
-    </list>
+    <list default="true" id="b30e2810-c4c1-4aad-b134-794e52cc1c7d" name="Changes" comment="fix: examples" />
     <option name="SHOW_DIALOG" value="false" />
     <option name="HIGHLIGHT_CONFLICTS" value="true" />
     <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
@@ -62,47 +55,47 @@
     <option name="hideEmptyMiddlePackages" value="true" />
     <option name="showLibraryContents" value="true" />
   </component>
-  <component name="PropertiesComponent"><![CDATA[{
-  "keyToString": {
-    "ASKED_ADD_EXTERNAL_FILES": "true",
-    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
-    "Docker.Dockerfile build.executor": "Run",
-    "Docker.Dockerfile.executor": "Run",
-    "Playwright.JoinText Component.executor": "Run",
-    "Playwright.JoinText Component.should merge text pieces with specified join character.executor": "Run",
-    "RunOnceActivity.OpenProjectViewOnStart": "true",
-    "RunOnceActivity.ShowReadmeOnStart": "true",
-    "RunOnceActivity.git.unshallow": "true",
-    "Vitest.compute function (1).executor": "Run",
-    "Vitest.compute function.executor": "Run",
-    "Vitest.mergeText.executor": "Run",
-    "Vitest.mergeText.should merge lines and preserve blank lines when deleteBlankLines is false.executor": "Run",
-    "Vitest.mergeText.should merge lines, preserve blank lines and trailing spaces when both deleteBlankLines and deleteTrailingSpaces are false.executor": "Run",
-    "git-widget-placeholder": "examples",
-    "ignore.virus.scanning.warn.message": "true",
-    "kotlin-language-version-configured": "true",
-    "last_opened_file_path": "C:/Users/<USER>/IdeaProjects/omni-tools/src/assets",
-    "node.js.detected.package.eslint": "true",
-    "node.js.detected.package.tslint": "true",
-    "node.js.selected.package.eslint": "(autodetect)",
-    "node.js.selected.package.tslint": "(autodetect)",
-    "nodejs_package_manager_path": "npm",
-    "npm.dev.executor": "Run",
-    "npm.lint.executor": "Run",
-    "npm.prebuild.executor": "Run",
-    "npm.script:create:tool.executor": "Run",
-    "npm.test.executor": "Run",
-    "npm.test:e2e.executor": "Run",
-    "npm.test:e2e:run.executor": "Run",
-    "prettierjs.PrettierConfiguration.Package": "C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\node_modules\\prettier",
-    "project.structure.last.edited": "Problems",
-    "project.structure.proportion": "0.0",
-    "project.structure.side.proportion": "0.2",
-    "settings.editor.selected.configurable": "settings.typescriptcompiler",
-    "ts.external.directory.path": "C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\node_modules\\typescript\\lib",
-    "vue.rearranger.settings.migration": "true"
+  <component name="PropertiesComponent">{
+  &quot;keyToString&quot;: {
+    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
+    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
+    &quot;Docker.Dockerfile build.executor&quot;: &quot;Run&quot;,
+    &quot;Docker.Dockerfile.executor&quot;: &quot;Run&quot;,
+    &quot;Playwright.JoinText Component.executor&quot;: &quot;Run&quot;,
+    &quot;Playwright.JoinText Component.should merge text pieces with specified join character.executor&quot;: &quot;Run&quot;,
+    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
+    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
+    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
+    &quot;Vitest.compute function (1).executor&quot;: &quot;Run&quot;,
+    &quot;Vitest.compute function.executor&quot;: &quot;Run&quot;,
+    &quot;Vitest.mergeText.executor&quot;: &quot;Run&quot;,
+    &quot;Vitest.mergeText.should merge lines and preserve blank lines when deleteBlankLines is false.executor&quot;: &quot;Run&quot;,
+    &quot;Vitest.mergeText.should merge lines, preserve blank lines and trailing spaces when both deleteBlankLines and deleteTrailingSpaces are false.executor&quot;: &quot;Run&quot;,
+    &quot;git-widget-placeholder&quot;: &quot;examples&quot;,
+    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
+    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
+    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/omni-tools/src/assets&quot;,
+    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
+    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
+    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
+    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
+    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
+    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
+    &quot;npm.lint.executor&quot;: &quot;Run&quot;,
+    &quot;npm.prebuild.executor&quot;: &quot;Run&quot;,
+    &quot;npm.script:create:tool.executor&quot;: &quot;Run&quot;,
+    &quot;npm.test.executor&quot;: &quot;Run&quot;,
+    &quot;npm.test:e2e.executor&quot;: &quot;Run&quot;,
+    &quot;npm.test:e2e:run.executor&quot;: &quot;Run&quot;,
+    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\node_modules\\prettier&quot;,
+    &quot;project.structure.last.edited&quot;: &quot;Problems&quot;,
+    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
+    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
+    &quot;settings.editor.selected.configurable&quot;: &quot;settings.typescriptcompiler&quot;,
+    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\node_modules\\typescript\\lib&quot;,
+    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
   }
-}]]></component>
+}</component>
   <component name="ReactDesignerToolWindowState">
     <option name="myId2Visible">
       <map>
@@ -265,15 +258,7 @@
       <workItem from="1740490890760" duration="1889000" />
       <workItem from="1740503199053" duration="4853000" />
       <workItem from="1740584243965" duration="17000" />
-      <workItem from="1740613094492" duration="9615000" />
-    </task>
-    <task id="LOCAL-00080" summary="fix: ci">
-      <option name="closed" value="true" />
-      <created>1719588378907</created>
-      <option name="number" value="00080" />
-      <option name="presentableId" value="LOCAL-00080" />
-      <option name="project" value="LOCAL" />
-      <updated>1719588378907</updated>
+      <workItem from="1740613094492" duration="10412000" />
     </task>
     <task id="LOCAL-00081" summary="fix: ci">
       <option name="closed" value="true" />
@@ -659,7 +644,15 @@
       <option name="project" value="LOCAL" />
       <updated>1740620866551</updated>
     </task>
-    <option name="localTasksCounter" value="129" />
+    <task id="LOCAL-00129" summary="fix: examples">
+      <option name="closed" value="true" />
+      <created>1740622869635</created>
+      <option name="number" value="00129" />
+      <option name="presentableId" value="LOCAL-00129" />
+      <option name="project" value="LOCAL" />
+      <updated>1740622869635</updated>
+    </task>
+    <option name="localTasksCounter" value="130" />
     <servers />
   </component>
   <component name="TypeScriptGeneratedFilesManager">
@@ -691,7 +684,6 @@
     <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
     <option name="CHECK_NEW_TODO" value="false" />
     <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
-    <MESSAGE value="chore: format number" />
     <MESSAGE value="feat: rotate ui" />
     <MESSAGE value="feat: shuffle ui" />
     <MESSAGE value="refactor: remove validation schema" />
@@ -716,7 +708,8 @@
     <MESSAGE value="chore: handle enter press on search" />
     <MESSAGE value="chore: show tooloptions in example" />
     <MESSAGE value="refact: examples" />
-    <option name="LAST_COMMIT_MESSAGE" value="refact: examples" />
+    <MESSAGE value="fix: examples" />
+    <option name="LAST_COMMIT_MESSAGE" value="fix: examples" />
   </component>
   <component name="XSLT-Support.FileAssociations.UIState">
     <expand />
