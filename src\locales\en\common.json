{"navigation": {"home": "Home", "categories": "Categories", "search": "Search all tools", "darkMode": "Dark Mode", "lightMode": "Light Mode", "changeLanguage": "Change Language", "buyMeACoffee": "Buy me a coffee"}, "hero": {"title": "Get Things Done Quickly with Omni Tools", "subtitle": "Your all-in-one toolkit for everyday tasks", "description": "Boost your productivity with Omni Tools, the ultimate toolkit for getting things done quickly! Access thousands of user-friendly utilities for editing images, text, lists, and data, all directly from your browser.", "searchPlaceholder": "Search all tools"}, "categories": {"string": {"title": "Text", "description": "Tools for working with text – convert text to images, find and replace text, split text into fragments, join text lines, repeat text, and much more."}, "png": {"description": "Tools for working with PNG images – convert PNGs to JPGs, create transparent PNGs, change PNG colors, crop, rotate, resize PNGs, and much more."}, "number": {"description": "Tools for working with numbers – generate number sequences, convert numbers to words and words to numbers, sort, round, factor numbers, and much more."}, "gif": {"description": "Tools for working with GIF animations – create transparent GIFs, extract GIF frames, add text to GIF, crop, rotate, reverse GIFs, and much more."}, "list": {"description": "Tools for working with lists – sort, reverse, randomize lists, find unique and duplicate list items, change list item separators, and much more."}, "json": {"description": "Tools for working with JSON data structures – prettify and minify JSON objects, flatten JSON arrays, stringify JSON values, analyze data, and much more."}, "time": {"description": "Tools for working with time and date – calculate time differences, convert between time zones, format dates, generate date sequences, and much more."}, "csv": {"description": "Tools for working with CSV files - convert CSV to different formats, manipulate CSV data, validate CSV structure, and process CSV files efficiently."}, "video": {"description": "Tools for working with videos – extract frames from videos, create GIFs from videos, convert videos to different formats, and much more."}, "pdf": {"description": "Tools for working with PDF files - extract text from PDFs, convert PDFs to other formats, manipulate PDFs, and much more."}, "image": {"title": "Image", "description": "Tools for working with pictures – compress, resize, crop, convert to JPG, rotate, remove background and much more."}}, "common": {"input": "Input", "output": "Output", "result": "Result", "options": "Options", "examples": "{{title}} Examples", "clickToTry": "Click to try!", "copy": "Copy", "paste": "Paste", "clear": "Clear", "download": "Download", "upload": "Upload", "import": "Import", "export": "Export", "save": "Save", "saveAs": "Save as", "loading": "Loading...", "processing": "Processing...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "cancel": "Cancel", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "reset": "Reset", "apply": "Apply", "preview": "Preview", "edit": "Edit", "delete": "Delete", "add": "Add", "remove": "Remove", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "file": "File", "files": "Files", "folder": "Folder", "folders": "Folders", "name": "Name", "size": "Size", "type": "Type", "format": "Format", "quality": "Quality", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "color": "Color", "colors": "Colors", "text": "Text", "image": "Image", "video": "Video", "audio": "Audio", "document": "Document", "seeAll": "See all", "try": "Try", "allTools": "All tools", "searchPlaceholder": "Search", "shortSignal": "Short Signal", "longSignal": "Long Signal", "dotSymbol": "Dot Symbol", "dashSymbol": "Dash Symbol", "morseCode": "Morse Code", "randomizedText": "Randomized Text", "uppercaseText": "Uppercase Text", "lowercaseText": "Lowercase Text", "selectFile": "Click here to select a file from your device, press Ctrl+V to use a file from your clipboard, or drag and drop a file from desktop", "dragDropFile": "Drop your file here", "fileSelected": "File selected", "noFileSelected": "No file selected", "supportedFormats": "Supported formats", "maxFileSize": "Max file size", "chooseFile": "Choose <PERSON>", "browse": "Browse", "defaultPrefix": "Default prefix", "unit": "Unit", "inputText": "Input Text"}, "toolActions": {"textCopied": "Text copied to clipboard", "failedToCopy": "Failed to copy"}, "toolInfo": {"whatIs": "What is a {{toolName}}?", "description": "Description", "features": "Features", "usage": "Usage", "examples": "Examples"}, "errors": {"fileNotSupported": "File type not supported", "fileTooLarge": "File is too large", "invalidInput": "Invalid input", "processingFailed": "Processing failed", "networkError": "Network error", "unknownError": "Unknown error occurred"}, "fileTypes": {"image": "Image", "video": "Video", "audio": "Audio", "pdf": "PDF", "document": "Document"}, "placeholders": {"enterText": "Enter your text here...", "selectOption": "Select an option", "chooseColor": "Choose a color", "exampleUrl": "https://example.com"}, "results": {"textWithoutDuplicates": "Text without duplicates", "randomizedText": "Randomized text", "quotedText": "Quoted Text", "reversedText": "Reversed Text", "rot13Text": "ROT13 Text", "rotatedText": "Rotated Text", "splitText": "Split Text", "joinedText": "Joined Text", "replacedText": "Replaced Text", "repeatedText": "Repeated Text", "truncatedText": "Truncated Text", "textStatistics": "Text Statistics", "palindromeResult": "Palindrome Check Result", "extractedSubstring": "Extracted Substring", "palindromeText": "Palindrome Text"}, "options": {"operationMode": "Operation Mode", "newlinesTabsSpaces": "Newlines, Tabs and Spaces", "sortLines": "Sort Lines", "quoteOptions": "Quote Options", "reversalOptions": "Reversal options", "rotationOptions": "Rotation Options", "splitOptions": "Split separator options", "outputOptions": "Output separator options", "textMergedOptions": "Text Merged Options", "blankLinesTrailingSpaces": "Blank Lines and Trailing Spaces", "textRepetitions": "Text Repetitions", "repetitionsDelimiter": "Repetitions Delimiter", "truncationSide": "Truncation Side", "lengthAndLines": "Length and Lines", "suffixAndAffix": "Suffix and Affix", "delimitersOptions": "Delimiters Options", "statisticsOptions": "Statistics Options", "splittingOptions": "Splitting options", "extractionOptions": "Extraction options", "palindromeOptions": "Palindrome options"}, "data": {"materialElectricalProperties": "Material Electrical Properties", "resistivityAt20C": "Resistivity at 20°C", "americanWireGauge": "American Wire Gauge", "diameter": "Diameter", "area": "Area"}, "inputImage": "Input Image", "extractedText": "Extracted Text", "enterUrl": "Enter the URL", "enterText": "Enter the text"}