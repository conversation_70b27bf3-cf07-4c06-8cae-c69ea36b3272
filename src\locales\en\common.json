{"navigation": {"home": "Home", "categories": "Categories", "search": "Search all tools", "darkMode": "Dark Mode", "lightMode": "Light Mode", "changeLanguage": "Change Language", "buyMeACoffee": "Buy me a coffee"}, "hero": {"title": "Get Things Done Quickly with Omni Tools", "subtitle": "Your all-in-one toolkit for everyday tasks", "description": "Boost your productivity with Omni Tools, the ultimate toolkit for getting things done quickly! Access thousands of user-friendly utilities for editing images, text, lists, and data, all directly from your browser.", "searchPlaceholder": "Search all tools"}, "categories": {"string": {"title": "Text", "description": "Tools for working with text – convert text to images, find and replace text, split text into fragments, join text lines, repeat text, and much more."}, "image-generic": {"title": "Image", "description": "Tools for working with images – resize, compress, convert formats, remove backgrounds, crop, change colors, and much more."}, "png": {"title": "PNG", "description": "Tools for working with PNG images – convert PNGs to JPGs, create transparent PNGs, change PNG colors, crop, rotate, resize PNGs, and much more."}, "number": {"title": "Numbers", "description": "Tools for working with numbers – generate number sequences, convert numbers to words and words to numbers, sort, round, factor numbers, and much more."}, "video": {"title": "Video", "description": "Tools for working with videos – trim, reverse, convert formats, extract frames, and much more."}, "list": {"title": "Lists", "description": "Tools for working with lists – sort, shuffle, filter, find unique items, group items, and much more."}, "json": {"title": "JSON", "description": "Tools for working with JSON data – format, validate, convert, extract values, and much more."}, "csv": {"title": "CSV", "description": "Tools for working with CSV files – convert, filter, sort, merge, and much more."}, "pdf": {"title": "PDF", "description": "Tools for working with PDF files – split, merge, compress, protect, convert, and much more."}, "time": {"title": "Time & Date", "description": "Tools for working with dates and time – calculate differences, convert formats, timezone conversions, and much more."}, "gif": {"title": "GIF", "description": "Tools for working with GIF images – create, edit, optimize, and much more."}}, "common": {"input": "Input", "output": "Output", "result": "Result", "options": "Options", "examples": "Examples", "clickToTry": "Click to try!", "copy": "Copy", "paste": "Paste", "clear": "Clear", "download": "Download", "upload": "Upload", "import": "Import", "export": "Export", "save": "Save", "saveAs": "Save as", "loading": "Loading...", "processing": "Processing...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "cancel": "Cancel", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "reset": "Reset", "apply": "Apply", "preview": "Preview", "edit": "Edit", "delete": "Delete", "add": "Add", "remove": "Remove", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "file": "File", "files": "Files", "folder": "Folder", "folders": "Folders", "name": "Name", "size": "Size", "type": "Type", "format": "Format", "quality": "Quality", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "color": "Color", "colors": "Colors", "text": "Text", "image": "Image", "video": "Video", "audio": "Audio", "document": "Document"}, "toolActions": {"copyToClipboard": "Copy to clipboard", "pasteFromClipboard": "Paste from clipboard", "importFromFile": "Import from file", "exportToFile": "Export to file", "textCopied": "Text copied", "failedToCopy": "Failed to copy", "fileImported": "File imported", "failedToImport": "Failed to import file"}, "toolInfo": {"whatIs": "What is a {{toolName}}?", "description": "Description", "features": "Features", "usage": "Usage", "examples": "Examples"}, "errors": {"fileNotSupported": "File type not supported", "fileTooLarge": "File is too large", "invalidInput": "Invalid input", "processingFailed": "Processing failed", "networkError": "Network error", "unknownError": "Unknown error occurred"}}