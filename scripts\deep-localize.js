#!/usr/bin/env node

/**
 * Deep Localization Script for OmniTools
 * 
 * This script performs comprehensive localization by:
 * 1. Localizing all meta.ts files
 * 2. Localizing service files with error messages
 * 3. Localizing data files with table headers
 * 4. Localizing remaining UI components
 * 5. Adding all translations to locale files
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class DeepLocalizer {
  constructor() {
    this.processedFiles = 0;
    this.modifiedFiles = 0;
    this.translations = {
      en: { tools: {}, common: {} },
      ru: { tools: {}, common: {} }
    };
    
    // Load existing translations
    this.loadExistingTranslations();
  }

  loadExistingTranslations() {
    try {
      const enTools = JSON.parse(fs.readFileSync('src/locales/en/tools.json', 'utf8'));
      const ruTools = JSON.parse(fs.readFileSync('src/locales/ru/tools.json', 'utf8'));
      const enCommon = JSON.parse(fs.readFileSync('src/locales/en/common.json', 'utf8'));
      const ruCommon = JSON.parse(fs.readFileSync('src/locales/ru/common.json', 'utf8'));
      
      this.translations.en.tools = enTools;
      this.translations.ru.tools = ruTools;
      this.translations.en.common = enCommon;
      this.translations.ru.common = ruCommon;
    } catch (error) {
      console.log('⚠️  Could not load existing translations:', error.message);
    }
  }

  // Extract tool path from file path
  getToolPath(filePath) {
    const match = filePath.match(/src[\/\\]pages[\/\\]tools[\/\\](.+)[\/\\]/);
    return match ? match[1].replace(/\\/g, '/') : null;
  }

  // Generate translation key from tool path
  getTranslationKey(toolPath) {
    return toolPath.replace(/\//g, '.');
  }

  // Simple translation to Russian
  translateToRussian(text) {
    const translations = {
      // Common words
      'Convert': 'Конвертировать', 'Generate': 'Генерировать', 'Compress': 'Сжать',
      'Resize': 'Изменить размер', 'Extract': 'Извлечь', 'Remove': 'Удалить',
      'Add': 'Добавить', 'Merge': 'Объединить', 'Split': 'Разделить',
      'Rotate': 'Повернуть', 'Crop': 'Обрезать', 'Upload': 'Загрузить',
      'Download': 'Скачать', 'Process': 'Обработать', 'Transform': 'Преобразовать',
      
      // File types
      'files': 'файлы', 'images': 'изображения', 'text': 'текст', 'data': 'данные',
      'CSV': 'CSV', 'JSON': 'JSON', 'XML': 'XML', 'PDF': 'PDF', 'image': 'изображение',
      
      // Actions
      'tool': 'инструмент', 'format': 'формат', 'with': 'с', 'and': 'и',
      'or': 'или', 'for': 'для', 'to': 'в', 'from': 'из', 'by': 'на',
      
      // UI elements
      'Input': 'Ввод', 'Output': 'Вывод', 'Result': 'Результат', 'Options': 'Настройки',
      'Settings': 'Настройки', 'Configuration': 'Конфигурация', 'Details': 'Детали',
      'Information': 'Информация', 'Description': 'Описание', 'Title': 'Заголовок',
      
      // Common phrases
      'Enter the': 'Введите', 'Select the': 'Выберите', 'Choose': 'Выберите',
      'Upload your': 'Загрузите ваш', 'This tool': 'Этот инструмент',
      'Easily': 'Легко', 'Quickly': 'Быстро', 'Simply': 'Просто',
      
      // Technical terms
      'delimiter': 'разделитель', 'separator': 'разделитель', 'character': 'символ',
      'encoding': 'кодировка', 'compression': 'сжатие', 'quality': 'качество',
      'resolution': 'разрешение', 'dimensions': 'размеры', 'aspect ratio': 'соотношение сторон'
    };
    
    let translated = text;
    for (const [en, ru] of Object.entries(translations)) {
      const regex = new RegExp(`\\b${en}\\b`, 'gi');
      translated = translated.replace(regex, ru);
    }
    
    return translated;
  }

  // Process meta files
  processMetaFile(filePath) {
    console.log(`Processing meta: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    const toolPath = this.getToolPath(filePath);
    if (!toolPath) return;
    
    const translationKey = this.getTranslationKey(toolPath);
    
    // Extract and replace description
    const descriptionMatch = content.match(/description:\s*['"`]([^'"`]+)['"`]/);
    if (descriptionMatch) {
      const description = descriptionMatch[1];
      
      // Store translations
      this.setNestedProperty(this.translations.en.tools, translationKey + '.description', description);
      this.setNestedProperty(this.translations.ru.tools, translationKey + '.description', this.translateToRussian(description));
      
      // Replace in content
      content = content.replace(
        /description:\s*['"`][^'"`]+['"`]/,
        `description: t('tools:${translationKey}.description')`
      );
      modified = true;
    }
    
    // Extract and replace shortDescription
    const shortDescMatch = content.match(/shortDescription:\s*['"`]([^'"`]+)['"`]/);
    if (shortDescMatch) {
      const shortDesc = shortDescMatch[1];
      
      this.setNestedProperty(this.translations.en.tools, translationKey + '.shortDescription', shortDesc);
      this.setNestedProperty(this.translations.ru.tools, translationKey + '.shortDescription', this.translateToRussian(shortDesc));
      
      content = content.replace(
        /shortDescription:\s*['"`][^'"`]+['"`]/,
        `shortDescription: t('tools:${translationKey}.shortDescription')`
      );
      modified = true;
    }
    
    // Add useTranslation if needed
    if (modified && !content.includes('useTranslation')) {
      content = "import { useTranslation } from 'react-i18next';\n" + content;
      
      // Add translation hook
      content = content.replace(
        /export\s+const\s+tool\s*=\s*defineTool\(/,
        'export const tool = defineTool('
      );
      
      // We need to modify the defineTool call to use translations
      // This is complex, so we'll handle it differently
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  // Helper to set nested object properties
  setNestedProperty(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  // Process service files
  processServiceFile(filePath) {
    console.log(`Processing service: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Common error messages to replace
    const errorReplacements = {
      "'Unknown error occurred'": "t('common.errors.unknownError')",
      '"Unknown error occurred"': "t('common.errors.unknownError')",
      "'File not supported'": "t('common.errors.fileNotSupported')",
      '"File not supported"': "t('common.errors.fileNotSupported')",
      "'Processing failed'": "t('common.errors.processingFailed')",
      '"Processing failed"': "t('common.errors.processingFailed')",
      "'Invalid input'": "t('common.errors.invalidInput')",
      '"Invalid input"': "t('common.errors.invalidInput')"
    };
    
    // Replace error messages
    for (const [oldStr, newStr] of Object.entries(errorReplacements)) {
      if (content.includes(oldStr)) {
        content = content.replace(new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newStr);
        modified = true;
      }
    }
    
    // Add useTranslation if needed
    if (modified && !content.includes('useTranslation')) {
      content = "import { useTranslation } from 'react-i18next';\n" + content;
      
      // Add translation hook to functions that use translations
      const functionMatches = content.match(/export\s+(function|const)\s+(\w+)/g);
      if (functionMatches) {
        // This is complex to handle automatically, we'll mark it for manual review
        console.log(`⚠️  Manual review needed for: ${filePath}`);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  // Process component files
  processComponentFile(filePath) {
    console.log(`Processing component: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Common UI strings to replace
    const uiReplacements = {
      "'Input Image'": "t('common.inputImage')",
      '"Input Image"': "t('common.inputImage')",
      "'Extracted Text'": "t('common.extractedText')",
      '"Extracted Text"': "t('common.extractedText')",
      "'Enter the URL'": "t('common.enterUrl')",
      '"Enter the URL"': "t('common.enterUrl')",
      "'Enter the text'": "t('common.enterText')",
      '"Enter the text"': "t('common.enterText')",
      "'Enter your text here'": "t('common.placeholders.enterText')",
      '"Enter your text here"': "t('common.placeholders.enterText')",
      "'https://example.com'": "t('common.placeholders.exampleUrl')",
      '"https://example.com"': "t('common.placeholders.exampleUrl')"
    };
    
    // Replace UI strings
    for (const [oldStr, newStr] of Object.entries(uiReplacements)) {
      if (content.includes(oldStr)) {
        content = content.replace(new RegExp(oldStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newStr);
        modified = true;
      }
    }
    
    // Add useTranslation if needed
    if (modified && !content.includes('useTranslation')) {
      content = content.replace(
        "import React",
        "import React"
      );
      
      if (content.includes("from 'react';")) {
        content = content.replace(
          "from 'react';",
          "from 'react';\nimport { useTranslation } from 'react-i18next';"
        );
      }
      
      // Add hook to component
      const componentMatch = content.match(/(export default function \w+\([^)]*\) \{[\s\n]*)/);
      if (componentMatch && !content.includes('const { t } = useTranslation();')) {
        content = content.replace(
          componentMatch[1],
          componentMatch[1] + '  const { t } = useTranslation();\n  '
        );
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  // Update translation files
  updateTranslationFiles() {
    console.log('\n📝 Updating translation files...');
    
    // Add missing common translations
    this.translations.en.common.inputImage = 'Input Image';
    this.translations.en.common.extractedText = 'Extracted Text';
    this.translations.en.common.enterUrl = 'Enter the URL';
    this.translations.en.common.enterText = 'Enter the text';
    
    this.translations.ru.common.inputImage = 'Входное изображение';
    this.translations.ru.common.extractedText = 'Извлеченный текст';
    this.translations.ru.common.enterUrl = 'Введите URL';
    this.translations.ru.common.enterText = 'Введите текст';
    
    // Add placeholder translations
    if (!this.translations.en.common.placeholders) {
      this.translations.en.common.placeholders = {};
      this.translations.ru.common.placeholders = {};
    }
    
    this.translations.en.common.placeholders.exampleUrl = 'https://example.com';
    this.translations.ru.common.placeholders.exampleUrl = 'https://example.com';
    
    // Write files
    fs.writeFileSync('src/locales/en/tools.json', JSON.stringify(this.translations.en.tools, null, 2), 'utf8');
    fs.writeFileSync('src/locales/ru/tools.json', JSON.stringify(this.translations.ru.tools, null, 2), 'utf8');
    fs.writeFileSync('src/locales/en/common.json', JSON.stringify(this.translations.en.common, null, 2), 'utf8');
    fs.writeFileSync('src/locales/ru/common.json', JSON.stringify(this.translations.ru.common, null, 2), 'utf8');
    
    console.log('✅ Updated translation files');
  }

  // Main processing function
  processAllFiles() {
    console.log('🚀 Starting deep localization...\n');
    
    // Process meta files
    const metaFiles = glob.sync('src/pages/tools/**/meta.ts');
    console.log(`Found ${metaFiles.length} meta files`);
    metaFiles.forEach(file => this.processMetaFile(file));
    
    // Process service files
    const serviceFiles = glob.sync('src/pages/tools/**/service.ts');
    console.log(`\nFound ${serviceFiles.length} service files`);
    serviceFiles.forEach(file => this.processServiceFile(file));
    
    // Process component files with hardcoded strings
    const componentFiles = glob.sync('src/pages/tools/**/index.tsx').filter(file => {
      const content = fs.readFileSync(file, 'utf8');
      return !content.includes('useTranslation') && (
        content.includes("'Input Image'") ||
        content.includes('"Input Image"') ||
        content.includes("'Extracted Text'") ||
        content.includes('"Extracted Text"') ||
        content.includes("'Enter the") ||
        content.includes('"Enter the')
      );
    });
    
    console.log(`\nFound ${componentFiles.length} component files with hardcoded strings`);
    componentFiles.forEach(file => this.processComponentFile(file));
    
    // Update translation files
    this.updateTranslationFiles();
    
    console.log('\n📊 SUMMARY:');
    console.log(`Total files processed: ${this.processedFiles}`);
    console.log(`Files modified: ${this.modifiedFiles}`);
    console.log(`Files skipped: ${this.processedFiles - this.modifiedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const localizer = new DeepLocalizer();
  localizer.processAllFiles();
  console.log('\n✅ Deep localization completed!');
}

module.exports = DeepLocalizer;
