import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Time to Seconds',
  path: 'convert-time-to-seconds',
  icon: 'mdi:tools',
  description: 'Time to Seconds tool for time operations. Perform convert time to seconds operations efficiently.',
  shortDescription: 'Time to Seconds tool for time operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
