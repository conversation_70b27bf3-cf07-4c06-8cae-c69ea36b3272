import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Time to Seconds',
  path: 'convert-time-to-seconds',
  icon: 'mdi:tools',
  description: 'With this browser-based application, you can convert clock time provided in hours, minutes, and seconds into just seconds. Given a time in HH:MM:SS format, it calculates HH*3600 + MM*60 + SS and prints this value in the output box. It supports AM/PM time formats as well as clock times beyond 24 hours.',
  shortDescription: 'Quickly convert clock time in H:M:S format to seconds.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
