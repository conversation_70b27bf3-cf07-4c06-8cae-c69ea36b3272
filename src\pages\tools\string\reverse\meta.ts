import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Reverse Text',
  path: 'reverse',
  icon: 'mdi:tools',
  description: 'Reverse Text tool for string operations. Perform reverse operations efficiently.',
  shortDescription: 'Reverse Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
