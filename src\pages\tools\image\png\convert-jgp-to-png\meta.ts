import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Convert Jgp To Png',
  path: 'convert-jgp-to-png',
  icon: 'mdi:tools',
  description: 'Convert Jgp To Png tool for image operations. Perform convert jgp to png operations efficiently.',
  shortDescription: 'Convert Jgp To Png tool for image operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
