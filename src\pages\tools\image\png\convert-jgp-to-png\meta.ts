import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Tool',
  path: 'convert-jgp-to-png',
  icon: 'mdi:tools',
  description: 'Quickly convert your JPG images to PNG. Just import your PNG image in the editor on the left',
  shortDescription: 'Quickly convert your JPG images to PNG',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
