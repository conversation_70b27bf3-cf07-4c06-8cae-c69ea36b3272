import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Tool',
  path: 'image-to-text',
  icon: 'mdi:tools',
  description: 'Extract text from images (JPG, PNG) using optical character recognition (OCR).',
  shortDescription: 'Extract text from images using OCR.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
