import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Image To Text',
  path: 'image-to-text',
  icon: 'mdi:tools',
  description: 'Extract text from images using OCR technology.',
  shortDescription: 'Extract text from images using OCR technology.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
