import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Csv To Tsv',
  path: 'csv-to-tsv',
  icon: 'mdi:tools',
  description: 'Csv To Tsv tool for csv operations. Perform csv to tsv operations efficiently.',
  shortDescription: 'Csv To Tsv tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
