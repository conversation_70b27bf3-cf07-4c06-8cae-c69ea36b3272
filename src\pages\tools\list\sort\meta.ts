import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Sort List',
  path: 'sort',
  icon: 'mdi:tools',
  description: 'Sort List tool for list operations. Perform sort operations efficiently.',
  shortDescription: 'Sort List tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
