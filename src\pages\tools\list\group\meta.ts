import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Group List',
  path: 'group',
  icon: 'mdi:tools',
  description: 'Group List tool for list operations. Perform group operations efficiently.',
  shortDescription: 'Group List tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
