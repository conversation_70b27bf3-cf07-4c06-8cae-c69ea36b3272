import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Stringify',
  path: 'stringify',
  icon: 'mdi:tools',
  description: 'Stringify tool for json operations. Perform stringify operations efficiently.',
  shortDescription: 'Stringify tool for json operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
