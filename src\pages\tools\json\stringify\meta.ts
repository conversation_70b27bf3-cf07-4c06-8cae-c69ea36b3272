import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Tool',
  path: 'stringify',
  icon: 'mdi:tools',
  description: 'Convert JavaScript objects and arrays into their JSON string representation. Options include custom indentation and HTML character escaping for web-safe JSON strings.',
  shortDescription: 'Convert JavaScript objects to JSON strings',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
