import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Find Unique Items',
  path: 'find-unique',
  icon: 'mdi:tools',
  description: 'Find Unique Items tool for list operations. Perform find unique operations efficiently.',
  shortDescription: 'Find Unique Items tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
