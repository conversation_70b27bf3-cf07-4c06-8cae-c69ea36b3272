{"string": {"to-morse": {"name": "String To Morse", "description": "World", "shortDescription": "Quickly encode text to morse", "dotDescription": "Symbol that will correspond to the dot in Morse code.", "dashDescription": "Symbol that will correspond to the dash in Morse code."}, "uppercase": {"name": "Uppercase Text", "description": "World", "shortDescription": "Convert text to uppercase letters", "example1": {"title": "Convert Text to Uppercase", "description": "This example transforms any text to ALL UPPERCASE format."}, "example2": {"title": "Uppercase Code", "description": "Convert code to uppercase format. Note that this is for display only and would not maintain code functionality."}, "example3": {"title": "Mixed Case to Uppercase", "description": "Transform text with mixed casing to consistent all uppercase format."}}, "lowercase": {"name": "Lowercase Text", "description": "Convert text to lowercase letters", "shortDescription": "Convert text to lowercase"}, "base64": {"name": "Base64 Encode/Decode", "description": "A simple tool to encode or decode data using Base64, which is commonly used in web applications.", "shortDescription": "Encode or decode data using Base64.", "optionsTitle": "Base64 Options", "encode": "Base64 Encode", "decode": "Base64 Decode", "example1": {"title": "Encode data in UTF-8 with Base64", "description": "This example shows how to encode a simple text using Base64."}, "example2": {"title": "Decode Base64-encoded data to UTF-8", "description": "This example shows how to decode data that was encoded with Base64."}}, "rot13": {"name": "ROT13 Encode/Decode", "description": "A simple tool to encode or decode text using the ROT13 cipher, which replaces each letter with the letter 13 positions after it in the alphabet.", "shortDescription": "Encode or decode text using ROT13 cipher."}, "join": {"name": "Join Text", "description": "World", "shortDescription": "Quickly merge texts"}, "split": {"name": "Split Text", "description": "World", "shortDescription": "Quickly split a text"}, "randomize-case": {"name": "Randomize Case", "description": "World", "shortDescription": "Convert text to random uppercase and lowercase letters"}, "palindrome": {"name": "Check Palindrome", "description": "World", "shortDescription": "Check if text reads the same forward and backward"}, "remove-duplicate-lines": {"name": "Remove Duplicate Lines", "description": "Load your text in the input form on the left and you", "shortDescription": "Quickly delete all repeated lines from text", "trimTextLines": "Trim Text Lines", "trimTextLinesDesc": "Before filtering uniques, remove tabs and spaces from the beginning and end of all lines.", "sortOutputLines": "Sort the Output Lines", "sortOutputLinesDesc": "After removing the duplicates, sort the unique lines."}, "quote": {"name": "Quote Text", "description": "A tool to add quotation marks or custom characters around text. Perfect for formatting strings for code, citations, or stylistic purposes.", "shortDescription": "Add quotes around text easily."}, "reverse": {"name": "Reverse Text", "description": "World", "shortDescription": "Reverse any text character by character"}, "rotate": {"name": "Rotate Text", "description": "A tool to rotate characters in a string by a specified number of positions. Shift characters left or right while maintaining their relative order.", "shortDescription": "Shift characters in text by position."}, "text-replacer": {"name": "Replace Text", "description": "Easily replace specific text in your content with this simple, browser-based tool. Just input your text, set the text you want to replace and the replacement value, and instantly get the updated version.", "shortDescription": "Quickly replace text in your content"}, "repeat": {"name": "Repeat Text", "description": "This tool allows you to repeat a given text multiple times with an optional separator.", "shortDescription": "Repeat text multiple times"}, "truncate": {"name": "Truncate Text", "description": "Load your text in the input form on the left and you will automatically get truncated text on the right.", "shortDescription": "Truncate your text easily"}, "statistic": {"name": "Text Statistics", "description": "Load your text in the input form on the left and you will automatically get statistics about your text on the right.", "shortDescription": "Get statistics about your text"}, "extract-substring": {"name": "Extract Substring", "description": "World", "shortDescription": "Extract specific portions of text by position and length"}, "create-palindrome": {"name": "Create Palindrome", "description": "World", "shortDescription": "Create text that reads the same forward and backward"}}, "csv": {"format": {"name": "Format CSV", "description": "Format and validate CSV files", "shortDescription": "Format CSV files"}, "change-csv-separator": {"description": "Just upload your CSV file in the form below and it will automatically get a new column delimiter character. In the tool options, you can specify which delimiter and quote characters are used in the source CSV file and customize the desired delimiter and quote characters for the output CSV. You can also filter the input CSV before the conversion process and skip blank lines and comment lines.", "shortDescription": "Quickly change the CSV column delimiter to a new symbol."}, "csv-rows-to-columns": {"description": "This tool converts rows of a CSV (Comma Separated Values) file into columns. It extracts the horizontal lines from the input CSV one by one, rotates them 90 degrees, and outputs them as vertical columns one after another, separated by commas.", "shortDescription": "Convert CSV rows to columns."}, "csv-to-json": {"description": "Convert CSV files to JSON format with customizable options for delimiters, quotes, and output formatting. Support for headers, comments, and dynamic type conversion.", "shortDescription": "Convert CSV data to JSON format."}, "csv-to-tsv": {"description": "Upload your CSV file in the form below and it will automatically get converted to a TSV file. In the tool options, you can customize the input CSV format – specify the field delimiter, quotation character, and comment symbol, as well as skip empty CSV lines, and choose whether to preserve CSV column headers.", "shortDescription": "Convert CSV data to TSV format."}, "csv-to-xml": {"description": "Convert CSV files to XML format with customizable options.", "shortDescription": "Convert CSV data to XML format."}, "csv-to-yaml": {"description": "Just upload your CSV file in the form below and it will automatically get converted to a YAML file. In the tool options, you can specify the field delimiter character, field quote character, and comment character to adapt the tool to custom CSV formats. Additionally, you can select the output YAML format: one that preserves CSV headers or one that excludes CSV headers.", "shortDescription": "Quickly convert a CSV file to a YAML file."}, "find-incomplete-csv-records": {"description": "Just upload your CSV file in the form below and this tool will automatically check if none of the rows or columns are missing values. In the tool options, you can adjust the input file format (specify the delimiter, quote character, and comment character). Additionally, you can enable checking for empty values, skip empty lines, and set a limit on the number of error messages in the output.", "shortDescription": "Quickly find rows and columns in CSV that are missing values."}, "insert-csv-columns": {"description": "Just upload your CSV file in the form below, paste the new column in the options, and it will automatically get inserted in your CSV. In the tool options, you can also specify more than one column to insert, set the insertion position, and optionally skip the empty and comment lines.", "shortDescription": "Quickly insert one or more new columns anywhere in a CSV file."}, "swap-csv-columns": {"description": "Just upload your CSV file in the form below, specify the columns to swap, and the tool will automatically change the positions of the specified columns in the output file. In the tool options, you can specify the column positions or names that you want to swap, as well as fix incomplete data and optionally remove empty records and records that have been commented out.", "shortDescription": "Reorder CSV columns."}, "transpose-csv": {"description": "Just upload your CSV file in the form below, and this tool will automatically transpose your CSV. In the tool options, you can specify the character that starts the comment lines in the CSV to remove them. Additionally, if the CSV is incomplete (missing values), you can replace missing values with the empty character or a custom character.", "shortDescription": "Quickly transpose a CSV file."}}, "image-generic": {"image-to-text": {"name": "Image to Text (OCR)", "description": "Extract text from images using OCR", "shortDescription": "Extract text from images"}, "resize": {"name": "Resize Image", "description": "Resize images to specific dimensions", "shortDescription": "Resize image dimensions"}, "compress": {"name": "Compress Image", "description": "Reduce image file size while maintaining quality", "shortDescription": "Compress image file size"}, "remove-background": {"name": "Remove Background", "description": "Remove background from images automatically", "shortDescription": "Remove image background"}, "crop": {"name": "Crop Image", "description": "Crop images to specific areas", "shortDescription": "Crop image area"}, "change-opacity": {"name": "Change Opacity", "description": "Adjust image transparency/opacity", "shortDescription": "Change image opacity"}, "change-colors": {"name": "Change Colors", "description": "Modify colors in images", "shortDescription": "Change image colors"}, "create-transparent": {"name": "Create Transparent", "description": "Make specific colors transparent in images", "shortDescription": "Create transparent image"}, "qr-code": {"name": "QR Code Generator", "description": "Generate QR codes for different data types", "shortDescription": "Generate QR codes"}}, "number": {"sum": {"name": "Sum Numbers", "description": "Quickly calculate the sum of numbers in your browser. To get your sum, just enter your list of numbers in the input field, adjust the separator between the numbers in the options below, and this utility will add up all these numbers.", "shortDescription": "Quickly sum numbers"}, "generate": {"name": "Generate Numbers", "description": "Quickly calculate a list of integers in your browser. To get your list, just specify the first integer, change value and total count in the options below, and this utility will generate that many integers", "shortDescription": "Quickly calculate a list of integers in your browser"}, "arithmetic-sequence": {"description": "Generate an arithmetic sequence by specifying the first term (a₁), common difference (d), and number of terms (n). The tool creates a sequence where each number differs from the previous by a constant difference.", "shortDescription": "Generate a sequence where each term differs by a constant value."}}, "list": {"sort": {"name": "Sort List", "description": "This is a super simple browser-based application that sorts items in a list and arranges them in increasing or decreasing order. You can sort the items alphabetically, numerically, or by their length. You can also remove duplicate and empty items, as well as trim individual items that have whitespace around them. You can use any separator character to separate the input list items or alternatively use a regular expression to separate them. Additionally, you can create a new delimiter for the sorted output list.", "shortDescription": "Quickly sort a list"}, "shuffle": {"name": "Shuffle List", "description": "A tool to randomly reorder items in a list. Perfect for randomizing data, creating random selections, or generating random sequences.", "shortDescription": "Randomly reorder list items."}, "find-unique": {"name": "Find Unique Items", "description": "World", "shortDescription": "Find unique items in a list"}, "group": {"name": "Group List", "description": "World", "shortDescription": "Group list items by common properties"}, "unwrap": {"name": "Unwrap List", "description": "A tool to remove characters from the beginning and end of each item in a list. Perfect for cleaning up formatted data or removing unwanted wrappers.", "shortDescription": "Remove characters around list items."}, "rotate": {"name": "Rotate List", "description": "A tool to rotate items in a list by a specified number of positions. Shift elements left or right while maintaining their relative order.", "shortDescription": "Shift list items by position."}, "duplicate": {"description": "A tool to duplicate each item in a list a specified number of times. Perfect for creating repeated patterns, test data, or expanding datasets.", "shortDescription": "Repeat items in a list multiple times."}, "find-most-popular": {"description": "A tool to identify and count the most frequently occurring items in a list. Useful for data analysis, finding trends, or identifying common elements.", "shortDescription": "Find most common items in a list."}, "reverse": {"description": "This is a super simple browser-based application prints all list items in reverse. The input items can be separated by any symbol and you can also change the separator of the reversed list items.", "shortDescription": "Quickly reverse a list"}, "truncate": {"description": "World", "shortDescription": "Limit the number of items in a list"}, "wrap": {"description": "A tool to wrap each item in a list with custom prefix and suffix characters. Useful for formatting lists for code, markup languages, or presentation.", "shortDescription": "Add characters around list items."}}, "json": {"prettify": {"name": "Prettify JSON", "description": "Just load your JSON in the input field and it will automatically get prettified. In the tool options, you can choose whether to use spaces or tabs for indentation and if you", "shortDescription": "Quickly beautify a JSON data structure."}, "escape-json": {"description": "Free online JSON escaper. Just load your JSON in the input field and it will automatically get escaped. In the tool options, you can optionally enable wrapping the escaped JSON in double quotes to get an escaped JSON string.", "shortDescription": "Quickly escape special JSON characters."}, "json-to-xml": {"description": "Convert JSON files to XML format with customizable options.", "shortDescription": "Convert JSON data to XML format"}, "minify": {"description": "Minify your JSON by removing all unnecessary whitespace and formatting. This tool compresses JSON data to its smallest possible size while maintaining valid JSON structure.", "shortDescription": "Quickly compress JSON file."}, "stringify": {"description": "Convert JavaScript objects and arrays into their JSON string representation. Options include custom indentation and HTML character escaping for web-safe JSON strings.", "shortDescription": "Convert JavaScript objects to JSON strings"}, "tsv-to-json": {"description": "Convert TSV files to JSON format with customizable options for delimiters, quotes, and output formatting. Support for headers, comments, and dynamic type conversion.", "shortDescription": "Convert TSV data to JSON format."}, "validateJson": {"description": "Validate JSON data and identify formatting issues such as missing quotes, trailing commas, and incorrect brackets.", "shortDescription": "Quickly validate a JSON data structure."}}, "pdf": {"split-pdf": {"name": "Split PDF", "description": "Extract specific pages from a PDF file using page numbers or ranges (e.g., 1,5-8)", "shortDescription": "Extract specific pages from a PDF file"}, "merge-pdf": {"name": "Merge PDF", "description": "Combine multiple PDF files into a single document.", "shortDescription": "Merge multiple PDF files into a single document"}, "rotate-pdf": {"name": "Rotate PDF", "description": "Rotate PDF pages by 90, 180, or 270 degrees", "shortDescription": "Rotate pages in a PDF document"}, "compress-pdf": {"name": "Compress PDF", "description": "Reduce PDF file size while maintaining quality using Ghostscript", "shortDescription": "Compress PDF files securely in your browser"}, "protect-pdf": {"name": "Protect PDF", "description": "Add password protection to your PDF files securely in your browser", "shortDescription": "Password protect PDF files securely"}, "pdf-to-epub": {"description": "Transform PDF documents into EPUB files for better e-reader compatibility.", "shortDescription": "Convert PDF files to EPUB format"}}, "time": {"time-between-dates": {"name": "Time Between Dates", "description": "Calculate the exact time difference between two dates and times, with support for different timezones. This tool provides a detailed breakdown of the time difference in various units (years, months, days, hours, minutes, and seconds).", "shortDescription": "Calculate the precise time duration between two dates with timezone support."}, "convert-days-to-hours": {"name": "Days to Hours", "description": "With this browser-based application, you can calculate how many hours there are in the given number of days. The application takes the input values (days), multiplies them by 24 and that converts them into hours. It supports both integer and decimal day values and it can convert multiple values at the same time.", "shortDescription": "Convert days to hours easily."}, "convert-hours-to-days": {"name": "Hours to Days", "description": "With this browser-based application, you can calculate how many days there are in the given number of hours. Given one or more hour values in the input, it converts them into days via the simple math formula: days = hours/24. It works with arbitrary large hour values and you can also customize the decimal day precision.", "shortDescription": "Convert hours to days easily."}, "convert-seconds-to-time": {"name": "Seconds to Time", "description": "With this browser-based application, you can convert seconds to clock time. Given the seconds input value, it converts them into full hours (H), minutes (M), and seconds (S) and prints them in human-readable clock format (H:M:S or HH:MM:SS) in the output field.", "shortDescription": "Quicky convert seconds to clock time in H:M:S format."}, "convert-time-to-seconds": {"name": "Time to Seconds", "description": "With this browser-based application, you can convert clock time provided in hours, minutes, and seconds into just seconds. Given a time in HH:MM:SS format, it calculates HH*3600 + MM*60 + SS and prints this value in the output box. It supports AM/PM time formats as well as clock times beyond 24 hours.", "shortDescription": "Quickly convert clock time in H:M:S format to seconds."}, "truncate-clock-time": {"name": "Truncate Clock Time", "description": "With this browser-based application, you can truncate a clock time and drop the minutes and/or seconds components from it. If you drop the seconds, you will be left with hours and minutes. For example, ", "shortDescription": "Quickly convert clock time in H:M:S format to seconds."}}, "video": {"trim": {"name": "Trim Video", "description": "This online utility lets you trim videos by setting start and end points. You can preview the trimmed section before processing. Supports common video formats like MP4, WebM, and OGG.", "shortDescription": "Trim videos by setting start and end points"}, "change-speed": {"description": "This online utility lets you change the speed of a video. You can speed it up or slow it down.", "shortDescription": "Quickly change video speed"}, "compress": {"description": "Compress videos by scaling them to different resolutions like 240p, 480p, 720p, etc. This tool helps reduce file size while maintaining acceptable quality. Supports common video formats like MP4, WebM, and OGG.", "shortDescription": "Compress videos by scaling to different resolutions"}, "crop-video": {"description": "Crop a video by specifying coordinates and dimensions", "shortDescription": "Crop video to specific area"}, "flip": {"description": "This online utility allows you to flip videos horizontally or vertically. You can preview the flipped video before processing. Supports common video formats like MP4, WebM, and OGG.", "shortDescription": "Flip videos horizontally or vertically"}, "gif": {"change-speed": {"description": "This online utility lets you change the speed of a GIF animation. You can speed it up or slow it down. You can set the same constant delay between all frames or change the delays of individual frames. You can also play both the input and output GIFs at the same time and compare their speeds", "shortDescription": "Quickly change GIF speed"}}, "loop": {"description": "This online utility lets you loop videos by specifying the number of repetitions. You can preview the looped video before processing. Supports common video formats like MP4, WebM, and OGG.", "shortDescription": "Loop videos multiple times"}, "rotate": {"description": "This online utility lets you rotate videos by 90, 180, or 270 degrees. You can preview the rotated video before processing. Supports common video formats like MP4, WebM, and OGG.", "shortDescription": "Rotate videos by 90, 180, or 270 degrees"}}, "gif": {"change-speed": {"name": "Change GIF Speed", "description": "Change the speed of GIF animations", "shortDescription": "Change GIF speed"}}, "png": {"compress-png": {"name": "Compress PNG", "description": "Compress PNG images to reduce file size", "shortDescription": "Compress PNG files"}, "convert-jgp-to-png": {"name": "Convert JPG to PNG", "description": "Convert JPG images to PNG format", "shortDescription": "Convert JPG to PNG"}}, "image": {"generic": {"change-colors": {"description": "World", "shortDescription": "Quickly swap colors in a image"}, "change-opacity": {"description": "Easily adjust the transparency of your images. Simply upload your image, use the slider to set the desired opacity level between 0 (fully transparent) and 1 (fully opaque), and download the modified image.", "shortDescription": "Adjust transparency of images"}, "compress": {"description": "Compress images to reduce file size while maintaining reasonable quality.", "shortDescription": "Compress images to reduce file size while maintaining reasonable quality."}, "create-transparent": {"description": "World", "shortDescription": "Quickly make an image transparent"}, "crop": {"description": "A tool to crop images with precision and ease.", "shortDescription": "Crop images quickly."}, "image-to-text": {"description": "Extract text from images (JPG, PNG) using optical character recognition (OCR).", "shortDescription": "Extract text from images using OCR."}, "qr-code": {"description": "Generate QR codes for different data types: URL, Text, Email, Phone, SMS, WiFi, vCard, and more.", "shortDescription": "Create customized QR codes for various data formats."}, "remove-background": {"description": "World", "shortDescription": "Automatically remove backgrounds from images"}, "resize": {"description": "Resize JPG, PNG, SVG or GIF images by pixels or percentage while maintaining aspect ratio or not.", "shortDescription": "Resize images easily."}}, "png": {"compress-png": {"description": "This is a program that compresses PNG pictures. As soon as you paste your PNG picture in the input area, the program will compress it and show the result in the output area. In the options, you can adjust the compression level, as well as find the old and new picture file sizes.", "shortDescription": "Quickly compress a PNG"}, "convert-jgp-to-png": {"description": "Quickly convert your JPG images to PNG. Just import your PNG image in the editor on the left", "shortDescription": "Quickly convert your JPG images to PNG"}}}}