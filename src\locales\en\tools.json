{"string": {"to-morse": {"name": "String To Morse", "description": "String To Morse tool for string operations. Perform to morse operations efficiently.", "shortDescription": "String To Morse tool for string operations.", "dotDescription": "Symbol that will correspond to the dot in Morse code.", "dashDescription": "Symbol that will correspond to the dash in Morse code."}, "uppercase": {"name": "Uppercase Text", "description": "Uppercase Text tool for string operations. Perform uppercase operations efficiently.", "shortDescription": "Uppercase Text tool for string operations.", "example1": {"title": "Convert Text to Uppercase", "description": "This example transforms any text to ALL UPPERCASE format."}, "example2": {"title": "Uppercase Code", "description": "Convert code to uppercase format. Note that this is for display only and would not maintain code functionality."}, "example3": {"title": "Mixed Case to Uppercase", "description": "Transform text with mixed casing to consistent all uppercase format."}}, "lowercase": {"name": "Lowercase Text", "description": "Convert text to lowercase letters", "shortDescription": "Convert text to lowercase"}, "base64": {"name": "Base64 Encode/Decode", "description": "Base64 Encode/Decode tool for string operations. Perform base64 operations efficiently.", "shortDescription": "Base64 Encode/Decode tool for string operations.", "optionsTitle": "Base64 Options", "encode": "Base64 Encode", "decode": "Base64 Decode", "example1": {"title": "Encode data in UTF-8 with Base64", "description": "This example shows how to encode a simple text using Base64."}, "example2": {"title": "Decode Base64-encoded data to UTF-8", "description": "This example shows how to decode data that was encoded with Base64."}}, "rot13": {"name": "ROT13 Encode/Decode", "description": "ROT13 Encode/Decode tool for string operations. Perform rot13 operations efficiently.", "shortDescription": "ROT13 Encode/Decode tool for string operations."}, "join": {"name": "Join Text", "description": "Join Text tool for string operations. Perform join operations efficiently.", "shortDescription": "Join Text tool for string operations."}, "split": {"name": "Split Text", "description": "Split Text tool for string operations. Perform split operations efficiently.", "shortDescription": "Split Text tool for string operations."}, "randomize-case": {"name": "Randomize Case", "description": "Randomize Case tool for string operations. Perform randomize case operations efficiently.", "shortDescription": "Randomize Case tool for string operations."}, "palindrome": {"name": "Check Palindrome", "description": "Check Palindrome tool for string operations. Perform palindrome operations efficiently.", "shortDescription": "Check Palindrome tool for string operations."}, "remove-duplicate-lines": {"name": "Remove Duplicate Lines", "description": "Remove Duplicate Lines tool for string operations. Perform remove duplicate lines operations efficiently.", "shortDescription": "Remove Duplicate Lines tool for string operations.", "trimTextLines": "Trim Text Lines", "trimTextLinesDesc": "Before filtering uniques, remove tabs and spaces from the beginning and end of all lines.", "sortOutputLines": "Sort the Output Lines", "sortOutputLinesDesc": "After removing the duplicates, sort the unique lines."}, "quote": {"name": "Quote Text", "description": "Quote Text tool for string operations. Perform quote operations efficiently.", "shortDescription": "Quote Text tool for string operations."}, "reverse": {"name": "Reverse Text", "description": "Reverse Text tool for string operations. Perform reverse operations efficiently.", "shortDescription": "Reverse Text tool for string operations.", "example1": {"title": "Simple Text Reversal", "description": "Reverses each character in the text. Perfect for creating mirror text."}, "example2": {"title": "Multi-line Reversal", "description": "Reverses each line independently while preserving the line breaks."}, "example3": {"title": "Clean Reversed Text", "description": "Trims whitespace and skips empty lines before reversing the text."}}, "rotate": {"name": "Rotate Text", "description": "Rotate Text tool for string operations. Perform rotate operations efficiently.", "shortDescription": "Rotate Text tool for string operations."}, "text-replacer": {"name": "Replace Text", "description": "Replace Text tool for string operations. Perform text replacer operations efficiently.", "shortDescription": "Replace Text tool for string operations."}, "repeat": {"name": "Repeat Text", "description": "Repeat Text tool for string operations. Perform repeat operations efficiently.", "shortDescription": "Repeat Text tool for string operations."}, "truncate": {"name": "Truncate Text", "description": "Truncate Text tool for string operations. Perform truncate operations efficiently.", "shortDescription": "Truncate Text tool for string operations."}, "statistic": {"name": "Text Statistics", "description": "Text Statistics tool for string operations. Perform statistic operations efficiently.", "shortDescription": "Text Statistics tool for string operations."}, "extract-substring": {"name": "Extract Substring", "description": "Extract Substring tool for string operations. Perform extract substring operations efficiently.", "shortDescription": "Extract Substring tool for string operations."}, "create-palindrome": {"name": "Create Palindrome", "description": "Create Palindrome tool for string operations. Perform create palindrome operations efficiently.", "shortDescription": "Create Palindrome tool for string operations."}}, "csv": {"format": {"name": "Format CSV", "description": "Format and validate CSV files", "shortDescription": "Format CSV files"}, "change-csv-separator": {"description": "Change Csv Separator tool for csv operations. Perform change csv separator operations efficiently.", "shortDescription": "Change Csv Separator tool for csv operations.", "name": "Change Csv Separator"}, "csv-rows-to-columns": {"description": "Csv Rows To Columns tool for csv operations. Perform csv rows to columns operations efficiently.", "shortDescription": "Csv Rows To Columns tool for csv operations.", "name": "Csv Rows To Columns"}, "csv-to-json": {"description": "Csv To Json tool for csv operations. Perform csv to json operations efficiently.", "shortDescription": "Csv To Json tool for csv operations.", "name": "Csv To Json"}, "csv-to-tsv": {"description": "Csv To Tsv tool for csv operations. Perform csv to tsv operations efficiently.", "shortDescription": "Csv To Tsv tool for csv operations.", "name": "Csv To Tsv"}, "csv-to-xml": {"description": "Csv To Xml tool for csv operations. Perform csv to xml operations efficiently.", "shortDescription": "Csv To Xml tool for csv operations.", "name": "Csv To Xml"}, "csv-to-yaml": {"description": "Csv To Yaml tool for csv operations. Perform csv to yaml operations efficiently.", "shortDescription": "Csv To Yaml tool for csv operations.", "name": "Csv To Yaml"}, "find-incomplete-csv-records": {"description": "Find Incomplete Csv Records tool for csv operations. Perform find incomplete csv records operations efficiently.", "shortDescription": "Find Incomplete Csv Records tool for csv operations.", "name": "Find Incomplete Csv Records"}, "insert-csv-columns": {"description": "Insert Csv Columns tool for csv operations. Perform insert csv columns operations efficiently.", "shortDescription": "Insert Csv Columns tool for csv operations.", "name": "Insert Csv Columns"}, "swap-csv-columns": {"description": "Swap Csv Columns tool for csv operations. Perform swap csv columns operations efficiently.", "shortDescription": "Swap Csv Columns tool for csv operations.", "name": "Swap Csv Columns"}, "transpose-csv": {"description": "Transpose Csv tool for csv operations. Perform transpose csv operations efficiently.", "shortDescription": "Transpose Csv tool for csv operations.", "name": "Transpose Csv"}}, "image-generic": {"image-to-text": {"name": "Image to Text (OCR)", "description": "Extract text from images using OCR", "shortDescription": "Extract text from images"}, "resize": {"name": "Resize Image", "description": "Resize images to specific dimensions", "shortDescription": "Resize image dimensions"}, "compress": {"name": "Compress Image", "description": "Reduce image file size while maintaining quality", "shortDescription": "Compress image file size"}, "remove-background": {"name": "Remove Background", "description": "Remove background from images automatically", "shortDescription": "Remove image background"}, "crop": {"name": "Crop Image", "description": "Crop images to specific areas", "shortDescription": "Crop image area"}, "change-opacity": {"name": "Change Opacity", "description": "Adjust image transparency/opacity", "shortDescription": "Change image opacity"}, "change-colors": {"name": "Change Colors", "description": "Modify colors in images", "shortDescription": "Change image colors"}, "create-transparent": {"name": "Create Transparent", "description": "Make specific colors transparent in images", "shortDescription": "Create transparent image"}, "qr-code": {"name": "QR Code Generator", "description": "Generate QR codes for different data types", "shortDescription": "Generate QR codes"}}, "number": {"sum": {"name": "Sum Numbers", "description": "Sum Numbers tool for number operations. Perform sum operations efficiently.", "shortDescription": "Sum Numbers tool for number operations."}, "generate": {"name": "Generate Numbers", "description": "Generate Numbers tool for number operations. Perform generate operations efficiently.", "shortDescription": "Generate Numbers tool for number operations."}, "arithmetic-sequence": {"description": "Arithmetic Sequence tool for number operations. Perform arithmetic sequence operations efficiently.", "shortDescription": "Arithmetic Sequence tool for number operations.", "name": "Arithmetic Sequence"}, "generic-calc": {"name": "Generic <PERSON>", "description": "Generic Calc tool for number operations. Perform generic calc operations efficiently.", "shortDescription": "Generic Calc tool for number operations."}}, "list": {"sort": {"name": "Sort List", "description": "Sort List tool for list operations. Perform sort operations efficiently.", "shortDescription": "Sort List tool for list operations."}, "shuffle": {"name": "Shuffle List", "description": "Shuffle List tool for list operations. Perform shuffle operations efficiently.", "shortDescription": "Shuffle List tool for list operations."}, "find-unique": {"name": "Find Unique Items", "description": "Find Unique Items tool for list operations. Perform find unique operations efficiently.", "shortDescription": "Find Unique Items tool for list operations."}, "group": {"name": "Group List", "description": "Group List tool for list operations. Perform group operations efficiently.", "shortDescription": "Group List tool for list operations."}, "unwrap": {"name": "Unwrap List", "description": "Unwrap List tool for list operations. Perform unwrap operations efficiently.", "shortDescription": "Unwrap List tool for list operations."}, "rotate": {"name": "Rotate List", "description": "Rotate List tool for list operations. Perform rotate operations efficiently.", "shortDescription": "Rotate List tool for list operations."}, "duplicate": {"description": "Duplicate tool for list operations. Perform duplicate operations efficiently.", "shortDescription": "Duplicate tool for list operations.", "name": "Duplicate"}, "find-most-popular": {"description": "Find Most Popular tool for list operations. Perform find most popular operations efficiently.", "shortDescription": "Find Most Popular tool for list operations.", "name": "Find Most Popular"}, "reverse": {"description": "Reverse tool for list operations. Perform reverse operations efficiently.", "shortDescription": "Reverse tool for list operations.", "name": "Reverse"}, "truncate": {"description": "Truncate tool for list operations. Perform truncate operations efficiently.", "shortDescription": "Truncate tool for list operations.", "name": "Truncate"}, "wrap": {"description": "Wrap tool for list operations. Perform wrap operations efficiently.", "shortDescription": "Wrap tool for list operations.", "name": "Wrap"}}, "json": {"prettify": {"name": "Prettify JSON", "description": "Prettify JSON tool for json operations. Perform prettify operations efficiently.", "shortDescription": "Prettify JSON tool for json operations."}, "escape-json": {"description": "Escape Json tool for json operations. Perform escape json operations efficiently.", "shortDescription": "Escape Json tool for json operations.", "name": "Escape Json"}, "json-to-xml": {"description": "Convert JSON data to XML format with customizable options.", "shortDescription": "Convert JSON data to XML format with customizable options.", "name": "Json To Xml"}, "minify": {"description": "Minify tool for json operations. Perform minify operations efficiently.", "shortDescription": "Minify tool for json operations.", "name": "Minify"}, "stringify": {"description": "Stringify tool for json operations. Perform stringify operations efficiently.", "shortDescription": "Stringify tool for json operations.", "name": "Stringify"}, "tsv-to-json": {"description": "Tsv To Json tool for json operations. Perform tsv to json operations efficiently.", "shortDescription": "Tsv To Json tool for json operations.", "name": "Tsv To Json"}, "validateJson": {"description": "ValidateJson tool for json operations. Perform validateJson operations efficiently.", "shortDescription": "ValidateJson tool for json operations.", "name": "Validate<PERSON>son"}}, "pdf": {"split-pdf": {"name": "Split PDF", "description": "Split PDF tool for pdf operations. Perform split pdf operations efficiently.", "shortDescription": "Split PDF tool for pdf operations."}, "merge-pdf": {"name": "Merge PDF", "description": "Merge PDF tool for pdf operations. Perform merge pdf operations efficiently.", "shortDescription": "Merge PDF tool for pdf operations."}, "rotate-pdf": {"name": "Rotate PDF", "description": "Rotate PDF tool for pdf operations. Perform rotate pdf operations efficiently.", "shortDescription": "Rotate PDF tool for pdf operations."}, "compress-pdf": {"name": "Compress PDF", "description": "Compress PDF tool for pdf operations. Perform compress pdf operations efficiently.", "shortDescription": "Compress PDF tool for pdf operations."}, "protect-pdf": {"name": "Protect PDF", "description": "Protect PDF tool for pdf operations. Perform protect pdf operations efficiently.", "shortDescription": "Protect PDF tool for pdf operations."}, "pdf-to-epub": {"description": "Pdf To Epub tool for pdf operations. Perform pdf to epub operations efficiently.", "shortDescription": "Pdf To Epub tool for pdf operations.", "name": "Pdf To Epub"}}, "time": {"time-between-dates": {"name": "Time Between Dates", "description": "Time Between Dates tool for time operations. Perform time between dates operations efficiently.", "shortDescription": "Time Between Dates tool for time operations."}, "convert-days-to-hours": {"name": "Days to Hours", "description": "Days to Hours tool for time operations. Perform convert days to hours operations efficiently.", "shortDescription": "Days to Hours tool for time operations."}, "convert-hours-to-days": {"name": "Hours to Days", "description": "Hours to Days tool for time operations. Perform convert hours to days operations efficiently.", "shortDescription": "Hours to Days tool for time operations."}, "convert-seconds-to-time": {"name": "Seconds to Time", "description": "Seconds to Time tool for time operations. Perform convert seconds to time operations efficiently.", "shortDescription": "Seconds to Time tool for time operations."}, "convert-time-to-seconds": {"name": "Time to Seconds", "description": "Time to Seconds tool for time operations. Perform convert time to seconds operations efficiently.", "shortDescription": "Time to Seconds tool for time operations."}, "truncate-clock-time": {"name": "Truncate Clock Time", "description": "Truncate Clock Time tool for time operations. Perform truncate clock time operations efficiently.", "shortDescription": "Truncate Clock Time tool for time operations."}}, "video": {"trim": {"name": "Trim Video", "description": "Trim Video tool for video operations. Perform trim operations efficiently.", "shortDescription": "Trim Video tool for video operations."}, "change-speed": {"description": "Change Speed tool for video operations. Perform change speed operations efficiently.", "shortDescription": "Change Speed tool for video operations.", "name": "Change Speed"}, "compress": {"description": "Compress tool for video operations. Perform compress operations efficiently.", "shortDescription": "Compress tool for video operations.", "name": "Compress"}, "crop-video": {"description": "Crop Video tool for video operations. Perform crop video operations efficiently.", "shortDescription": "Crop Video tool for video operations.", "name": "Crop Video"}, "flip": {"description": "Flip tool for video operations. Perform flip operations efficiently.", "shortDescription": "Flip tool for video operations.", "name": "Flip"}, "gif": {"change-speed": {"description": "Change Speed tool for video operations. Perform change speed operations efficiently.", "shortDescription": "Change Speed tool for video operations.", "name": "Change Speed"}}, "loop": {"description": "Loop tool for video operations. Perform loop operations efficiently.", "shortDescription": "Loop tool for video operations.", "name": "Loop"}, "rotate": {"description": "Rotate tool for video operations. Perform rotate operations efficiently.", "shortDescription": "Rotate tool for video operations.", "name": "Rotate"}}, "gif": {"change-speed": {"name": "Change GIF Speed", "description": "Change the speed of GIF animations", "shortDescription": "Change GIF speed"}}, "png": {"compress-png": {"name": "Compress PNG", "description": "Compress PNG images to reduce file size", "shortDescription": "Compress PNG files"}, "convert-jgp-to-png": {"name": "Convert JPG to PNG", "description": "Convert JPG images to PNG format", "shortDescription": "Convert JPG to PNG"}}, "image": {"generic": {"change-colors": {"description": "Change Colors tool for image operations. Perform change colors operations efficiently.", "shortDescription": "Change Colors tool for image operations.", "name": "Change Colors"}, "change-opacity": {"description": "Change Opacity tool for image operations. Perform change opacity operations efficiently.", "shortDescription": "Change Opacity tool for image operations.", "name": "Change Opacity"}, "compress": {"description": "Compress images to reduce file size while maintaining quality.", "shortDescription": "Compress images to reduce file size while maintaining quality.", "name": "Compress"}, "create-transparent": {"description": "Create Transparent tool for image operations. Perform create transparent operations efficiently.", "shortDescription": "Create Transparent tool for image operations.", "name": "Create Transparent"}, "crop": {"description": "Crop images to remove unwanted areas.", "shortDescription": "Crop images to remove unwanted areas.", "name": "Crop"}, "image-to-text": {"description": "Extract text from images using OCR technology.", "shortDescription": "Extract text from images using OCR technology.", "name": "Image To Text"}, "qr-code": {"description": "Generate QR codes from text or URLs.", "shortDescription": "Generate QR codes from text or URLs.", "name": "Qr Code"}, "remove-background": {"description": "Remove background from images automatically.", "shortDescription": "Remove background from images automatically.", "name": "Remove Background"}, "resize": {"description": "Resize images to specific dimensions.", "shortDescription": "Resize images to specific dimensions.", "name": "Resize"}}, "png": {"compress-png": {"description": "Compress Png tool for image operations. Perform compress png operations efficiently.", "shortDescription": "Compress Png tool for image operations.", "name": "Compress Png"}, "convert-jgp-to-png": {"description": "Convert Jgp To Png tool for image operations. Perform convert jgp to png operations efficiently.", "shortDescription": "Convert Jgp To Png tool for image operations.", "name": "Convert Jgp To Png"}}}}