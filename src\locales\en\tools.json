{"string": {"to-morse": {"name": "String To Morse", "description": "World's simplest browser-based utility for converting text to Morse code. Load your text in the input form on the left and you'll instantly get Morse code in the output area. Powerful, free, and fast. Load text – get Morse code.", "shortDescription": "Quickly encode text to morse", "dotDescription": "Symbol that will correspond to the dot in Morse code.", "dashDescription": "Symbol that will correspond to the dash in Morse code."}, "uppercase": {"name": "Uppercase Text", "description": "Convert text to uppercase letters", "shortDescription": "Convert text to uppercase", "example1": {"title": "Convert Text to Uppercase", "description": "This example transforms any text to ALL UPPERCASE format."}, "example2": {"title": "Uppercase Code", "description": "Convert code to uppercase format. Note that this is for display only and would not maintain code functionality."}, "example3": {"title": "Mixed Case to Uppercase", "description": "Transform text with mixed casing to consistent all uppercase format."}}, "lowercase": {"name": "Lowercase Text", "description": "Convert text to lowercase letters", "shortDescription": "Convert text to lowercase"}, "base64": {"name": "Base64 Encode/Decode", "description": "Encode or decode text using Base64 encoding", "shortDescription": "Base64 encode/decode text", "optionsTitle": "Base64 Options", "encode": "Base64 Encode", "decode": "Base64 Decode", "example1": {"title": "Encode data in UTF-8 with Base64", "description": "This example shows how to encode a simple text using Base64."}, "example2": {"title": "Decode Base64-encoded data to UTF-8", "description": "This example shows how to decode data that was encoded with Base64."}}, "rot13": {"name": "ROT13 Encode/Decode", "description": "Encode or decode text using ROT13 cipher", "shortDescription": "ROT13 encode/decode text"}, "join": {"name": "Join Text", "description": "Join multiple text lines into a single line", "shortDescription": "Join text lines together"}, "split": {"name": "Split Text", "description": "Split text into multiple lines or parts", "shortDescription": "Split text into parts"}, "randomize-case": {"name": "Randomize Case", "description": "Randomly change the case of letters in text", "shortDescription": "Randomize text case"}, "palindrome": {"name": "Check Palindrome", "description": "Check if text is a palindrome", "shortDescription": "Check if text is palindrome"}, "remove-duplicate-lines": {"name": "Remove Duplicate Lines", "description": "Remove duplicate lines from text", "shortDescription": "Remove duplicate lines", "trimTextLines": "Trim Text Lines", "trimTextLinesDesc": "Before filtering uniques, remove tabs and spaces from the beginning and end of all lines.", "sortOutputLines": "Sort the Output Lines", "sortOutputLinesDesc": "After removing the duplicates, sort the unique lines."}, "quote": {"name": "Quote Text", "description": "Add quotes around text", "shortDescription": "Quote text"}}, "image-generic": {"resize": {"name": "Resize Image", "description": "Resize images to specific dimensions", "shortDescription": "Resize image dimensions"}, "compress": {"name": "Compress Image", "description": "Reduce image file size while maintaining quality", "shortDescription": "Compress image file size"}, "remove-background": {"name": "Remove Background", "description": "Remove background from images automatically", "shortDescription": "Remove image background"}, "crop": {"name": "Crop Image", "description": "Crop images to specific areas", "shortDescription": "Crop image area"}, "change-opacity": {"name": "Change Opacity", "description": "Adjust image transparency/opacity", "shortDescription": "Change image opacity"}, "change-colors": {"name": "Change Colors", "description": "Modify colors in images", "shortDescription": "Change image colors"}, "create-transparent": {"name": "Create Transparent", "description": "Make specific colors transparent in images", "shortDescription": "Create transparent image"}, "image-to-text": {"name": "Image to Text (OCR)", "description": "Extract text from images using OCR", "shortDescription": "Extract text from images"}, "qr-code": {"name": "QR Code Generator", "description": "Generate QR codes for different data types", "shortDescription": "Generate QR codes"}}, "number": {"sum": {"name": "Sum Numbers", "description": "Calculate the sum of numbers", "shortDescription": "Calculate number sum"}, "generate": {"name": "Generate Numbers", "description": "Generate sequences of numbers", "shortDescription": "Generate number sequences"}}, "list": {"sort": {"name": "Sort List", "description": "Sort list items in various orders", "shortDescription": "Sort list items"}, "shuffle": {"name": "Shuffle List", "description": "Randomly shuffle list items", "shortDescription": "Shuffle list randomly"}, "find-unique": {"name": "Find Unique Items", "description": "Find unique items in a list", "shortDescription": "Find unique list items"}, "group": {"name": "Group List", "description": "Group list items into chunks", "shortDescription": "Group list items"}, "unwrap": {"name": "Unwrap List", "description": "Remove wrapping characters from list items", "shortDescription": "Unwrap list items"}, "rotate": {"name": "Rotate List", "description": "Rotate list items left or right", "shortDescription": "Rotate list items"}}, "json": {"prettify": {"name": "Prettify JSON", "description": "Format and prettify JSON data", "shortDescription": "Format JSON data"}}, "pdf": {"split-pdf": {"name": "Split PDF", "description": "Split PDF files into separate pages", "shortDescription": "Split PDF pages"}, "merge-pdf": {"name": "Merge PDF", "description": "Merge multiple PDF files into one", "shortDescription": "Merge PDF files"}, "rotate-pdf": {"name": "Rotate PDF", "description": "Rotate PDF pages", "shortDescription": "Rotate PDF pages"}, "compress-pdf": {"name": "Compress PDF", "description": "Reduce PDF file size", "shortDescription": "Compress PDF size"}, "protect-pdf": {"name": "Protect PDF", "description": "Add password protection to PDF", "shortDescription": "Password protect PDF"}}, "time": {"time-between-dates": {"name": "Time Between Dates", "description": "Calculate time difference between dates", "shortDescription": "Calculate date difference"}, "convert-days-to-hours": {"name": "Days to Hours", "description": "Convert days to hours", "shortDescription": "Convert days to hours"}, "convert-hours-to-days": {"name": "Hours to Days", "description": "Convert hours to days", "shortDescription": "Convert hours to days"}, "convert-seconds-to-time": {"name": "Seconds to Time", "description": "Convert seconds to time format", "shortDescription": "Convert seconds to time"}, "convert-time-to-seconds": {"name": "Time to Seconds", "description": "Convert time format to seconds", "shortDescription": "Convert time to seconds"}, "truncate-clock-time": {"name": "Truncate Clock Time", "description": "Truncate time to specific precision", "shortDescription": "Truncate clock time"}}, "video": {"trim": {"name": "Trim Video", "description": "Trim video to specific duration", "shortDescription": "Trim video duration"}}, "csv": {"format": {"name": "Format CSV", "description": "Format and validate CSV files", "shortDescription": "Format CSV files"}}, "gif": {"change-speed": {"name": "Change GIF Speed", "description": "Change the speed of GIF animations", "shortDescription": "Change GIF speed"}}, "png": {"compress-png": {"name": "Compress PNG", "description": "Compress PNG images to reduce file size", "shortDescription": "Compress PNG files"}, "convert-jgp-to-png": {"name": "Convert JPG to PNG", "description": "Convert JPG images to PNG format", "shortDescription": "Convert JPG to PNG"}}}