import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Unwrap List',
  path: 'unwrap',
  icon: 'mdi:tools',
  description: 'A tool to remove characters from the beginning and end of each item in a list. Perfect for cleaning up formatted data or removing unwanted wrappers.',
  shortDescription: 'Remove characters around list items.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
