import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Unwrap List',
  path: 'unwrap',
  icon: 'mdi:tools',
  description: 'Unwrap List tool for list operations. Perform unwrap operations efficiently.',
  shortDescription: 'Unwrap List tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
