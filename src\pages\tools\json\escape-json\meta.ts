import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Tool',
  path: 'escape-json',
  icon: 'mdi:tools',
  description: 'Free online JSON escaper. Just load your JSON in the input field and it will automatically get escaped. In the tool options, you can optionally enable wrapping the escaped JSON in double quotes to get an escaped JSON string.',
  shortDescription: 'Quickly escape special JSON characters.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
