import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Split Text',
  path: 'split',
  icon: 'mdi:tools',
  description: 'Split Text tool for string operations. Perform split operations efficiently.',
  shortDescription: 'Split Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
