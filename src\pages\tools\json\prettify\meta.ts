import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Prettify JSON',
  path: 'prettify',
  icon: 'mdi:tools',
  description: 'Just load your JSON in the input field and it will automatically get prettified. In the tool options, you can choose whether to use spaces or tabs for indentation and if you',
  shortDescription: 'Quickly beautify a JSON data structure.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
