import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Shuffle List',
  path: 'shuffle',
  icon: 'mdi:tools',
  description: 'Shuffle List tool for list operations. Perform shuffle operations efficiently.',
  shortDescription: 'Shuffle List tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
