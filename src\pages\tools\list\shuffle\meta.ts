import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Shuffle List',
  path: 'shuffle',
  icon: 'mdi:tools',
  description: 'A tool to randomly reorder items in a list. Perfect for randomizing data, creating random selections, or generating random sequences.',
  shortDescription: 'Randomly reorder list items.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
