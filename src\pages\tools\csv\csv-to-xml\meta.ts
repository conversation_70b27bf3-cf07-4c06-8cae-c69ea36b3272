import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Csv To Xml',
  path: 'csv-to-xml',
  icon: 'mdi:tools',
  description: 'Csv To Xml tool for csv operations. Perform csv to xml operations efficiently.',
  shortDescription: 'Csv To Xml tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
