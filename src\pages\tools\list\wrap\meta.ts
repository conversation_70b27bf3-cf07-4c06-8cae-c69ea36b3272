import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Wrap',
  path: 'wrap',
  icon: 'mdi:tools',
  description: 'Wrap tool for list operations. Perform wrap operations efficiently.',
  shortDescription: 'Wrap tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
