import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Tool',
  path: 'wrap',
  icon: 'mdi:tools',
  description: 'A tool to wrap each item in a list with custom prefix and suffix characters. Useful for formatting lists for code, markup languages, or presentation.',
  shortDescription: 'Add characters around list items.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
