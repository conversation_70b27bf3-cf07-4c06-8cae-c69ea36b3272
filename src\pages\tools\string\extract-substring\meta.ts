import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Extract Substring',
  path: 'extract-substring',
  icon: 'mdi:tools',
  description: 'Extract Substring tool for string operations. Perform extract substring operations efficiently.',
  shortDescription: 'Extract Substring tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
