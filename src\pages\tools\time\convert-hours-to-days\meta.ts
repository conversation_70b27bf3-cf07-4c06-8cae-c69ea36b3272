import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('time', {
  name: 'Hours to Days',
  path: 'convert-hours-to-days',
  icon: 'mdi:tools',
  description: 'With this browser-based application, you can calculate how many days there are in the given number of hours. Given one or more hour values in the input, it converts them into days via the simple math formula: days = hours/24. It works with arbitrary large hour values and you can also customize the decimal day precision.',
  shortDescription: 'Convert hours to days easily.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
