<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b30e2810-c4c1-4aad-b134-794e52cc1c7d" name="Changes" comment="feat: qr code generation init">
      <change afterPath="$PROJECT_DIR$/src/pages/tools/json/tsv-to-json/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/tools/json/tsv-to-json/meta.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/tools/json/tsv-to-json/service.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/tools/json/tsv-to-json/types.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/tools/json/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/tools/json/index.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FormatOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="origin/main" />
  </component>
  <component name="Git.Settings">
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;history&quot;: [
    {
      &quot;assignee&quot;: &quot;iib0011&quot;
    },
    {
      &quot;state&quot;: &quot;OPEN&quot;
    }
  ],
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;
  }
}</component>
  <component name="GitHubPullRequestState">{
  &quot;prStates&quot;: [
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts51PkS9&quot;,
        &quot;number&quot;: 22
      },
      &quot;lastSeen&quot;: 1741207144695
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6NiNYl&quot;,
        &quot;number&quot;: 32
      },
      &quot;lastSeen&quot;: 1741209723869
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6Nheyd&quot;,
        &quot;number&quot;: 31
      },
      &quot;lastSeen&quot;: 1741213371410
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6NmRBs&quot;,
        &quot;number&quot;: 33
      },
      &quot;lastSeen&quot;: 1741282429036
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts5zyFTs&quot;,
        &quot;number&quot;: 15
      },
      &quot;lastSeen&quot;: 1741535540953
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6QQB3c&quot;,
        &quot;number&quot;: 59
      },
      &quot;lastSeen&quot;: 1743018960900
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6QMPEg&quot;,
        &quot;number&quot;: 58
      },
      &quot;lastSeen&quot;: 1743019452983
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6QZvRI&quot;,
        &quot;number&quot;: 61
      },
      &quot;lastSeen&quot;: 1743103196866
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6QqPrQ&quot;,
        &quot;number&quot;: 73
      },
      &quot;lastSeen&quot;: 1743265865001
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6Qp5nI&quot;,
        &quot;number&quot;: 72
      },
      &quot;lastSeen&quot;: 1743338472110
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6QsjlS&quot;,
        &quot;number&quot;: 76
      },
      &quot;lastSeen&quot;: 1743352150953
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6Q0JBe&quot;,
        &quot;number&quot;: 82
      },
      &quot;lastSeen&quot;: 1743470267269
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6UE9-x&quot;,
        &quot;number&quot;: 102
      },
      &quot;lastSeen&quot;: 1747171977348
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6XPua_&quot;,
        &quot;number&quot;: 117
      },
      &quot;lastSeen&quot;: 1747929835864
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6XY-mZ&quot;,
        &quot;number&quot;: 119
      },
      &quot;lastSeen&quot;: 1748028108508
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6Xdz4n&quot;,
        &quot;number&quot;: 120
      },
      &quot;lastSeen&quot;: *************
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6X_zxl&quot;,
        &quot;number&quot;: 131
      },
      &quot;lastSeen&quot;: *************
    },
    {
      &quot;id&quot;: {
        &quot;id&quot;: &quot;PR_kwDOMJIfts6XsHfL&quot;,
        &quot;number&quot;: 128
      },
      &quot;lastSeen&quot;: *************
    }
  ]
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/iib0011/omni-tools.git&quot;,
    &quot;accountId&quot;: &quot;45f8cd51-000f-4ba4-a4c6-c4d96ac9b1e5&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/node_modules/react-image-crop/dist/index.d.ts" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiProvider">{
    &quot;isMigrated&quot;: true
    }</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
    &quot;associatedIndex&quot;: 0
    }</component>
  <component name="ProjectId" id="2i6g3WkUxdCURKYvUIJ9LMY5Qsc" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Docker.Dockerfile build.executor": "Run",
    "Docker.Dockerfile.executor": "Run",
    "Playwright.Create transparent PNG.should make png color transparent.executor": "Run",
    "Playwright.JoinText Component.executor": "Run",
    "Playwright.JoinText Component.should merge text pieces with specified join character.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Vitest.compute function (1).executor": "Run",
    "Vitest.compute function.executor": "Run",
    "Vitest.mergeText.executor": "Run",
    "Vitest.mergeText.should merge lines and preserve blank lines when deleteBlankLines is false.executor": "Run",
    "Vitest.mergeText.should merge lines, preserve blank lines and trailing spaces when both deleteBlankLines and deleteTrailingSpaces are false.executor": "Run",
    "Vitest.parsePageRanges.executor": "Run",
    "Vitest.removeDuplicateLines function.executor": "Run",
    "Vitest.removeDuplicateLines function.newlines option.executor": "Run",
    "Vitest.removeDuplicateLines function.newlines option.should filter newlines when newlines is set to filter.executor": "Run",
    "Vitest.replaceText function (regexp mode).should return the original text when passed an invalid regexp.executor": "Run",
    "Vitest.replaceText function.executor": "Run",
    "Vitest.timeBetweenDates.executor": "Run",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/IdeaProjects/omni-tools/src/pages/tools/json",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.build.executor": "Run",
    "npm.dev.executor": "Run",
    "npm.lint.executor": "Run",
    "npm.prebuild.executor": "Run",
    "npm.script:create:tool.executor": "Run",
    "npm.test.executor": "Run",
    "npm.test:e2e.executor": "Run",
    "npm.test:e2e:run.executor": "Run",
    "prettierjs.PrettierConfiguration.Package": "C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\node_modules\\prettier",
    "project.structure.last.edited": "Problems",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "refactai_advanced_settings",
    "ts.external.directory.path": "C:\\Users\\<USER>\\IdeaProjects\\omni-tools\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactDesignerToolWindowState">
    <option name="myId2Visible">
      <map>
        <entry key="com.intellij.reactbuddy.reactComponents" value="false" />
        <entry key="com.intellij.reactbuddy.reactInspector" value="false" />
        <entry key="com.intellij.reactbuddy.storybook" value="false" />
      </map>
    </option>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\src\pages\tools\json" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\src" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\@types" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\public\assets" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\src\components\input" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\src\lib\ghostscript" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\@types" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\public\assets" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\src\pages\tools" />
      <recent name="C:\Users\<USER>\IdeaProjects\omni-tools\src\pages\categories" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="calculateTimeBetweenDates" type="JavaScriptTestRunnerVitest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
      <working-dir value="$PROJECT_DIR$" />
      <vitest-options value="--run" />
      <envs />
      <scope-kind value="SUITE" />
      <test-file value="$PROJECT_DIR$/src/pages/tools/time/time-between-dates/time-between-dates.service.test.ts" />
      <test-names>
        <test-name value="calculateTimeBetweenDates" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration name="parsePageRanges" type="JavaScriptTestRunnerVitest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
      <working-dir value="$PROJECT_DIR$" />
      <vitest-options value="--run" />
      <envs />
      <scope-kind value="SUITE" />
      <test-file value="$PROJECT_DIR$/src/pages/tools/pdf/split-pdf/service.test.ts" />
      <test-names>
        <test-name value="parsePageRanges" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration name="replaceText function (regexp mode).should return the original text when passed an invalid regexp" type="JavaScriptTestRunnerVitest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
      <working-dir value="$PROJECT_DIR$" />
      <vitest-options value="--run" />
      <envs />
      <scope-kind value="TEST" />
      <test-file value="$PROJECT_DIR$/src/pages/tools/string/text-replacer/replaceText.service.test.ts" />
      <test-names>
        <test-name value="replaceText function (regexp mode)" />
        <test-name value="should return the original text when passed an invalid regexp" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration name="timeBetweenDates" type="JavaScriptTestRunnerVitest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
      <working-dir value="$PROJECT_DIR$" />
      <vitest-options value="--run" />
      <envs />
      <scope-kind value="SUITE" />
      <test-file value="$PROJECT_DIR$/src/pages/tools/time/time-between-dates/time-between-dates.service.test.ts" />
      <test-names>
        <test-name value="timeBetweenDates" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.dev" />
      <item itemvalue="Vitest.calculateTimeBetweenDates" />
      <item itemvalue="Vitest.timeBetweenDates" />
      <item itemvalue="Vitest.parsePageRanges" />
      <item itemvalue="Vitest.replaceText function (regexp mode).should return the original text when passed an invalid regexp" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="Vitest.replaceText function (regexp mode).should return the original text when passed an invalid regexp" />
        <item itemvalue="Vitest.parsePageRanges" />
        <item itemvalue="Vitest.timeBetweenDates" />
        <item itemvalue="Vitest.calculateTimeBetweenDates" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.24978.46" />
        <option value="bundled-js-predefined-d6986cc7102b-76f8388c3a79-JavaScript-IU-243.24978.46" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b30e2810-c4c1-4aad-b134-794e52cc1c7d" name="Changes" comment="" />
      <created>1718816243156</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1718816243156</updated>
      <workItem from="1718816244509" duration="13061000" />
      <workItem from="1718991004992" duration="25000" />
      <workItem from="1718991057845" duration="85000" />
      <workItem from="1718991144614" duration="6968000" />
      <workItem from="1718998317252" duration="8533000" />
      <workItem from="1719006887776" duration="7000" />
      <workItem from="1719006951159" duration="2377000" />
      <workItem from="1719021128819" duration="3239000" />
      <workItem from="1719083989394" duration="7971000" />
      <workItem from="1719092003308" duration="14856000" />
      <workItem from="1719164664347" duration="2033000" />
      <workItem from="1719166718305" duration="1783000" />
      <workItem from="1719168519203" duration="17675000" />
      <workItem from="1719197816332" duration="1453000" />
      <workItem from="1719273044735" duration="9847000" />
      <workItem from="1719294110005" duration="3842000" />
      <workItem from="1719339559458" duration="303000" />
      <workItem from="1719340295244" duration="772000" />
      <workItem from="1719363272227" duration="390000" />
      <workItem from="1719379971872" duration="8943000" />
      <workItem from="1719464673797" duration="38000" />
      <workItem from="1719475764139" duration="14903000" />
      <workItem from="1719492452780" duration="8000" />
      <workItem from="1719496624579" duration="6148000" />
      <workItem from="1720542757452" duration="5355000" />
      <workItem from="1720557527691" duration="3245000" />
      <workItem from="1720564427492" duration="1523000" />
      <workItem from="1720613598176" duration="8000" />
      <workItem from="1720655252208" duration="3975000" />
      <workItem from="1720661825389" duration="4305000" />
      <workItem from="1720729165596" duration="3258000" />
      <workItem from="1720911748039" duration="331000" />
      <workItem from="1720912096050" duration="3065000" />
      <workItem from="1740259920741" duration="7742000" />
      <workItem from="1740270391152" duration="690000" />
      <workItem from="1740274898695" duration="2231000" />
      <workItem from="1740295530385" duration="1120000" />
      <workItem from="1740300354462" duration="1059000" />
      <workItem from="1740301493702" duration="8924000" />
      <workItem from="1740318886545" duration="856000" />
      <workItem from="1740348963270" duration="388000" />
      <workItem from="1740399426653" duration="627000" />
      <workItem from="1740459961271" duration="66000" />
      <workItem from="1740460036909" duration="8299000" />
      <workItem from="1740490890760" duration="1889000" />
      <workItem from="1740503199053" duration="4853000" />
      <workItem from="1740584243965" duration="17000" />
      <workItem from="1740613094492" duration="9615000" />
      <workItem from="1740664266923" duration="145000" />
      <workItem from="1740665190253" duration="496000" />
      <workItem from="1740670449847" duration="4776000" />
      <workItem from="1740702343843" duration="657000" />
      <workItem from="1740788381920" duration="465000" />
      <workItem from="1740788856134" duration="659000" />
      <workItem from="1740880919391" duration="4395000" />
      <workItem from="1740923024259" duration="23000" />
      <workItem from="1740933006573" duration="3679000" />
      <workItem from="1741475969294" duration="4215000" />
      <workItem from="1741494053121" duration="178000" />
      <workItem from="1741537936314" duration="1294000" />
      <workItem from="1741539602311" duration="4557000" />
      <workItem from="1741547560596" duration="1671000" />
      <workItem from="1741567442768" duration="14127000" />
      <workItem from="1741971589699" duration="371000" />
      <workItem from="1743018497879" duration="3895000" />
      <workItem from="1743047367993" duration="986000" />
      <workItem from="1743103182313" duration="4264000" />
      <workItem from="1743348610793" duration="21855000" />
      <workItem from="1743397561176" duration="25000" />
      <workItem from="1743458576265" duration="13083000" />
      <workItem from="1743690613245" duration="77000" />
      <workItem from="1743691250813" duration="1550000" />
      <workItem from="1743699386059" duration="11195000" />
      <workItem from="1743782726563" duration="2444000" />
      <workItem from="1743811558991" duration="1279000" />
      <workItem from="1745523972292" duration="3000" />
      <workItem from="1745687713234" duration="1747000" />
      <workItem from="1745775228478" duration="1221000" />
      <workItem from="1745835676024" duration="68000" />
      <workItem from="1747171958176" duration="1105000" />
      <workItem from="1747217211469" duration="4000" />
      <workItem from="1747929815472" duration="843000" />
      <workItem from="1748026506667" duration="2536000" />
      <workItem from="1748282636141" duration="478000" />
      <workItem from="1749047510481" duration="879000" />
    </task>
    <task id="LOCAL-00152" summary="feat: crop png">
      <option name="closed" value="true" />
      <created>1741492688761</created>
      <option name="number" value="00152" />
      <option name="presentableId" value="LOCAL-00152" />
      <option name="project" value="LOCAL" />
      <updated>1741492688761</updated>
    </task>
    <task id="LOCAL-00153" summary="chore: remove unnecessary files">
      <option name="closed" value="true" />
      <created>1741492943849</created>
      <option name="number" value="00153" />
      <option name="presentableId" value="LOCAL-00153" />
      <option name="project" value="LOCAL" />
      <updated>1741492943849</updated>
    </task>
    <task id="LOCAL-00154" summary="refactor: validateJson">
      <option name="closed" value="true" />
      <created>1741535390090</created>
      <option name="number" value="00154" />
      <option name="presentableId" value="LOCAL-00154" />
      <option name="project" value="LOCAL" />
      <updated>1741535390090</updated>
    </task>
    <task id="LOCAL-00155" summary="refactor: use ToolContent">
      <option name="closed" value="true" />
      <created>1741540939154</created>
      <option name="number" value="00155" />
      <option name="presentableId" value="LOCAL-00155" />
      <option name="project" value="LOCAL" />
      <updated>1741540939154</updated>
    </task>
    <task id="LOCAL-00156" summary="feat: missing tools">
      <option name="closed" value="true" />
      <created>1741542318259</created>
      <option name="number" value="00156" />
      <option name="presentableId" value="LOCAL-00156" />
      <option name="project" value="LOCAL" />
      <updated>1741542318259</updated>
    </task>
    <task id="LOCAL-00157" summary="refactor: use ToolContent">
      <option name="closed" value="true" />
      <created>1741543593426</created>
      <option name="number" value="00157" />
      <option name="presentableId" value="LOCAL-00157" />
      <option name="project" value="LOCAL" />
      <updated>1741543593427</updated>
    </task>
    <task id="LOCAL-00158" summary="fix: prettify json">
      <option name="closed" value="true" />
      <created>1741543732607</created>
      <option name="number" value="00158" />
      <option name="presentableId" value="LOCAL-00158" />
      <option name="project" value="LOCAL" />
      <updated>1741543732607</updated>
    </task>
    <task id="LOCAL-00159" summary="refactor: sum">
      <option name="closed" value="true" />
      <created>1741544086061</created>
      <option name="number" value="00159" />
      <option name="presentableId" value="LOCAL-00159" />
      <option name="project" value="LOCAL" />
      <updated>1741544086061</updated>
    </task>
    <task id="LOCAL-00160" summary="fix: tools by category scroll">
      <option name="closed" value="true" />
      <created>1741548044897</created>
      <option name="number" value="00160" />
      <option name="presentableId" value="LOCAL-00160" />
      <option name="project" value="LOCAL" />
      <updated>1741548044897</updated>
    </task>
    <task id="LOCAL-00161" summary="fix: missing meta">
      <option name="closed" value="true" />
      <created>1741568170877</created>
      <option name="number" value="00161" />
      <option name="presentableId" value="LOCAL-00161" />
      <option name="project" value="LOCAL" />
      <updated>1741568170877</updated>
    </task>
    <task id="LOCAL-00162" summary="feat: trim video">
      <option name="closed" value="true" />
      <created>1741580004784</created>
      <option name="number" value="00162" />
      <option name="presentableId" value="LOCAL-00162" />
      <option name="project" value="LOCAL" />
      <updated>1741580004784</updated>
    </task>
    <task id="LOCAL-00163" summary="feat: trim video">
      <option name="closed" value="true" />
      <created>1741580736359</created>
      <option name="number" value="00163" />
      <option name="presentableId" value="LOCAL-00163" />
      <option name="project" value="LOCAL" />
      <updated>1741580736359</updated>
    </task>
    <task id="LOCAL-00164" summary="refactor: file inputs">
      <option name="closed" value="true" />
      <created>1742960931740</created>
      <option name="number" value="00164" />
      <option name="presentableId" value="LOCAL-00164" />
      <option name="project" value="LOCAL" />
      <updated>1742960931740</updated>
    </task>
    <task id="LOCAL-00165" summary="feat: background removal">
      <option name="closed" value="true" />
      <created>1742961898820</created>
      <option name="number" value="00165" />
      <option name="presentableId" value="LOCAL-00165" />
      <option name="project" value="LOCAL" />
      <updated>1742961898820</updated>
    </task>
    <task id="LOCAL-00166" summary="feat: split pdf">
      <option name="closed" value="true" />
      <created>1742967844908</created>
      <option name="number" value="00166" />
      <option name="presentableId" value="LOCAL-00166" />
      <option name="project" value="LOCAL" />
      <updated>1742967844908</updated>
    </task>
    <task id="LOCAL-00167" summary="fix: typo">
      <option name="closed" value="true" />
      <created>1743019312699</created>
      <option name="number" value="00167" />
      <option name="presentableId" value="LOCAL-00167" />
      <option name="project" value="LOCAL" />
      <updated>1743019312699</updated>
    </task>
    <task id="LOCAL-00168" summary="chore: result file name">
      <option name="closed" value="true" />
      <created>1743020690384</created>
      <option name="number" value="00168" />
      <option name="presentableId" value="LOCAL-00168" />
      <option name="project" value="LOCAL" />
      <updated>1743020690384</updated>
    </task>
    <task id="LOCAL-00169" summary="chore: text result extensions">
      <option name="closed" value="true" />
      <created>1743022260639</created>
      <option name="number" value="00169" />
      <option name="presentableId" value="LOCAL-00169" />
      <option name="project" value="LOCAL" />
      <updated>1743022260639</updated>
    </task>
    <task id="LOCAL-00170" summary="chore: show new tools in landing">
      <option name="closed" value="true" />
      <created>1743051792459</created>
      <option name="number" value="00170" />
      <option name="presentableId" value="LOCAL-00170" />
      <option name="project" value="LOCAL" />
      <updated>1743051792459</updated>
    </task>
    <task id="LOCAL-00171" summary="chore: zoom on hover">
      <option name="closed" value="true" />
      <created>1743052111988</created>
      <option name="number" value="00171" />
      <option name="presentableId" value="LOCAL-00171" />
      <option name="project" value="LOCAL" />
      <updated>1743052111988</updated>
    </task>
    <task id="LOCAL-00172" summary="refactor: time between dates">
      <option name="closed" value="true" />
      <created>1743106796406</created>
      <option name="number" value="00172" />
      <option name="presentableId" value="LOCAL-00172" />
      <option name="project" value="LOCAL" />
      <updated>1743106796406</updated>
    </task>
    <task id="LOCAL-00173" summary="fix: typos">
      <option name="closed" value="true" />
      <created>1743349732644</created>
      <option name="number" value="00173" />
      <option name="presentableId" value="LOCAL-00173" />
      <option name="project" value="LOCAL" />
      <updated>1743349732644</updated>
    </task>
    <task id="LOCAL-00174" summary="feat: compress video">
      <option name="closed" value="true" />
      <created>1743355099396</created>
      <option name="number" value="00174" />
      <option name="presentableId" value="LOCAL-00174" />
      <option name="project" value="LOCAL" />
      <updated>1743355099396</updated>
    </task>
    <task id="LOCAL-00175" summary="chore: compress video icon">
      <option name="closed" value="true" />
      <created>1743355166425</created>
      <option name="number" value="00175" />
      <option name="presentableId" value="LOCAL-00175" />
      <option name="project" value="LOCAL" />
      <updated>1743355166426</updated>
    </task>
    <task id="LOCAL-00176" summary="fix: gif speed">
      <option name="closed" value="true" />
      <created>1743385388051</created>
      <option name="number" value="00176" />
      <option name="presentableId" value="LOCAL-00176" />
      <option name="project" value="LOCAL" />
      <updated>1743385388051</updated>
    </task>
    <task id="LOCAL-00177" summary="fix: tsc">
      <option name="closed" value="true" />
      <created>1743385467178</created>
      <option name="number" value="00177" />
      <option name="presentableId" value="LOCAL-00177" />
      <option name="project" value="LOCAL" />
      <updated>1743385467178</updated>
    </task>
    <task id="LOCAL-00178" summary="fix: background color">
      <option name="closed" value="true" />
      <created>1743385898871</created>
      <option name="number" value="00178" />
      <option name="presentableId" value="LOCAL-00178" />
      <option name="project" value="LOCAL" />
      <updated>1743385898871</updated>
    </task>
    <task id="LOCAL-00179" summary="docs: github trendings">
      <option name="closed" value="true" />
      <created>1743459110471</created>
      <option name="number" value="00179" />
      <option name="presentableId" value="LOCAL-00179" />
      <option name="project" value="LOCAL" />
      <updated>1743459110471</updated>
    </task>
    <task id="LOCAL-00180" summary="docs: optimize">
      <option name="closed" value="true" />
      <created>1743459205311</created>
      <option name="number" value="00180" />
      <option name="presentableId" value="LOCAL-00180" />
      <option name="project" value="LOCAL" />
      <updated>1743459205311</updated>
    </task>
    <task id="LOCAL-00181" summary="fix: stars button width for 1k+ 😊">
      <option name="closed" value="true" />
      <created>1743470832619</created>
      <option name="number" value="00181" />
      <option name="presentableId" value="LOCAL-00181" />
      <option name="project" value="LOCAL" />
      <updated>1743470832619</updated>
    </task>
    <task id="LOCAL-00182" summary="feat: compress pdf">
      <option name="closed" value="true" />
      <created>1743644598841</created>
      <option name="number" value="00182" />
      <option name="presentableId" value="LOCAL-00182" />
      <option name="project" value="LOCAL" />
      <updated>1743644598841</updated>
    </task>
    <task id="LOCAL-00183" summary="refactor: compress pdf">
      <option name="closed" value="true" />
      <created>1743644703041</created>
      <option name="number" value="00183" />
      <option name="presentableId" value="LOCAL-00183" />
      <option name="project" value="LOCAL" />
      <updated>1743644703042</updated>
    </task>
    <task id="LOCAL-00184" summary="refactor: lib">
      <option name="closed" value="true" />
      <created>1743644942488</created>
      <option name="number" value="00184" />
      <option name="presentableId" value="LOCAL-00184" />
      <option name="project" value="LOCAL" />
      <updated>1743644942488</updated>
    </task>
    <task id="LOCAL-00185" summary="fix: path">
      <option name="closed" value="true" />
      <created>1743645074051</created>
      <option name="number" value="00185" />
      <option name="presentableId" value="LOCAL-00185" />
      <option name="project" value="LOCAL" />
      <updated>1743645074051</updated>
    </task>
    <task id="LOCAL-00186" summary="fix: vite worker format">
      <option name="closed" value="true" />
      <created>1743647707334</created>
      <option name="number" value="00186" />
      <option name="presentableId" value="LOCAL-00186" />
      <option name="project" value="LOCAL" />
      <updated>1743647707334</updated>
    </task>
    <task id="LOCAL-00187" summary="fix: tests">
      <option name="closed" value="true" />
      <created>1743691399769</created>
      <option name="number" value="00187" />
      <option name="presentableId" value="LOCAL-00187" />
      <option name="project" value="LOCAL" />
      <updated>1743691399769</updated>
    </task>
    <task id="LOCAL-00188" summary="chore: uninstall @jspawn/ghostscript-wasm">
      <option name="closed" value="true" />
      <created>1743691471368</created>
      <option name="number" value="00188" />
      <option name="presentableId" value="LOCAL-00188" />
      <option name="project" value="LOCAL" />
      <updated>1743691471368</updated>
    </task>
    <task id="LOCAL-00189" summary="feat: protect pdf">
      <option name="closed" value="true" />
      <created>1743705749057</created>
      <option name="number" value="00189" />
      <option name="presentableId" value="LOCAL-00189" />
      <option name="project" value="LOCAL" />
      <updated>1743705749057</updated>
    </task>
    <task id="LOCAL-00190" summary="feat: image to text">
      <option name="closed" value="true" />
      <created>1743710133267</created>
      <option name="number" value="00190" />
      <option name="presentableId" value="LOCAL-00190" />
      <option name="project" value="LOCAL" />
      <updated>1743710133267</updated>
    </task>
    <task id="LOCAL-00191" summary="chore: hideCopy if video or audio">
      <option name="closed" value="true" />
      <created>1743710669869</created>
      <option name="number" value="00191" />
      <option name="presentableId" value="LOCAL-00191" />
      <option name="project" value="LOCAL" />
      <updated>1743710669869</updated>
    </task>
    <task id="LOCAL-00192" summary="chore: readme img and fix broken link">
      <option name="closed" value="true" />
      <created>1743811980098</created>
      <option name="number" value="00192" />
      <option name="presentableId" value="LOCAL-00192" />
      <option name="project" value="LOCAL" />
      <updated>1743811980098</updated>
    </task>
    <task id="LOCAL-00193" summary="fix: add mkv to supported videos">
      <option name="closed" value="true" />
      <created>1745688369521</created>
      <option name="number" value="00193" />
      <option name="presentableId" value="LOCAL-00193" />
      <option name="project" value="LOCAL" />
      <updated>1745688369521</updated>
    </task>
    <task id="LOCAL-00194" summary="feat: drag and drop">
      <option name="closed" value="true" />
      <created>1745688866294</created>
      <option name="number" value="00194" />
      <option name="presentableId" value="LOCAL-00194" />
      <option name="project" value="LOCAL" />
      <updated>1745688866294</updated>
    </task>
    <task id="LOCAL-00195" summary="fix: misc">
      <option name="closed" value="true" />
      <created>1747172914927</created>
      <option name="number" value="00195" />
      <option name="presentableId" value="LOCAL-00195" />
      <option name="project" value="LOCAL" />
      <updated>1747172914927</updated>
    </task>
    <task id="LOCAL-00196" summary="chore: revert create-tool.mjs">
      <option name="closed" value="true" />
      <created>1748027090253</created>
      <option name="number" value="00196" />
      <option name="presentableId" value="LOCAL-00196" />
      <option name="project" value="LOCAL" />
      <updated>1748027090253</updated>
    </task>
    <task id="LOCAL-00197" summary="fix: misc">
      <option name="closed" value="true" />
      <created>1748027889103</created>
      <option name="number" value="00197" />
      <option name="presentableId" value="LOCAL-00197" />
      <option name="project" value="LOCAL" />
      <updated>1748027889103</updated>
    </task>
    <task id="LOCAL-00198" summary="chore: remove unnecessary prop">
      <option name="closed" value="true" />
      <created>1748028055669</created>
      <option name="number" value="00198" />
      <option name="presentableId" value="LOCAL-00198" />
      <option name="project" value="LOCAL" />
      <updated>1748028055669</updated>
    </task>
    <task id="LOCAL-00199" summary="fix: compute flow">
      <option name="closed" value="true" />
      <created>1748881153433</created>
      <option name="number" value="00199" />
      <option name="presentableId" value="LOCAL-00199" />
      <option name="project" value="LOCAL" />
      <updated>1748881153433</updated>
    </task>
    <task id="LOCAL-00200" summary="feat: qr code generation init">
      <option name="closed" value="true" />
      <created>1749147227565</created>
      <option name="number" value="00200" />
      <option name="presentableId" value="LOCAL-00200" />
      <option name="project" value="LOCAL" />
      <updated>1749147227565</updated>
    </task>
    <option name="localTasksCounter" value="201" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/examples" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="fix: gif speed" />
    <MESSAGE value="fix: tsc" />
    <MESSAGE value="fix: background color" />
    <MESSAGE value="docs: github trendings" />
    <MESSAGE value="docs: optimize" />
    <MESSAGE value="fix: stars button width for 1k+ " />
    <MESSAGE value="feat: compress pdf" />
    <MESSAGE value="refactor: compress pdf" />
    <MESSAGE value="refactor: lib" />
    <MESSAGE value="fix: path" />
    <MESSAGE value="fix: vite worker format" />
    <MESSAGE value="fix: tests" />
    <MESSAGE value="chore: uninstall @jspawn/ghostscript-wasm" />
    <MESSAGE value="feat: protect pdf" />
    <MESSAGE value="feat: image to text" />
    <MESSAGE value="chore: hideCopy if video or audio" />
    <MESSAGE value="chore: readme img and fix broken link" />
    <MESSAGE value="fix: add mkv to supported videos" />
    <MESSAGE value="feat: drag and drop" />
    <MESSAGE value="Merge branch 'feat/pdf-merge' of git-rohit:rohit267/omni-tools into feat/pdf-merge" />
    <MESSAGE value="chore: revert create-tool.mjs" />
    <MESSAGE value="fix: misc" />
    <MESSAGE value="chore: remove unnecessary prop" />
    <MESSAGE value="fix: compute flow" />
    <MESSAGE value="feat: qr code generation init" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: qr code generation init" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>