#!/usr/bin/env node

/**
 * Fix Broken Components Script
 * 
 * This script fixes components that were broken by the auto-localize script
 * by restoring proper function parameters and fixing syntax errors.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class ComponentFixer {
  constructor() {
    this.fixedFiles = 0;
  }

  fixFile(filePath) {
    console.log(`Fixing: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix broken function parameters like ($2)
    const brokenParamMatch = content.match(/export default function (\w+)\(\$2\)/);
    if (brokenParamMatch) {
      content = content.replace(
        /export default function (\w+)\(\$2\)/,
        'export default function $1({ title }: ToolComponentProps)'
      );
      modified = true;
    }
    
    // Fix missing title parameter in other cases
    const missingTitleMatch = content.match(/export default function (\w+)\(\)/);
    if (missingTitleMatch) {
      content = content.replace(
        /export default function (\w+)\(\)/,
        'export default function $1({ title }: ToolComponentProps)'
      );
      modified = true;
    }
    
    // Fix title= without curly braces
    content = content.replace(/title=t\(/g, 'title={t(');
    content = content.replace(/title=t\('([^']+)'\)/g, "title={t('$1')}");
    
    // Fix missing closing braces for title props
    content = content.replace(/title=\{t\('([^']+)'\) /g, "title={t('$1')} ");
    
    // Ensure ToolComponentProps import exists
    if (content.includes('{ title }: ToolComponentProps') && !content.includes('import { ToolComponentProps }')) {
      // Find existing ToolComponentProps import and add it if missing
      if (content.includes("from '@tools/defineTool';")) {
        content = content.replace(
          "from '@tools/defineTool';",
          "from '@tools/defineTool';"
        );
      } else {
        content = content.replace(
          "import { useTranslation } from 'react-i18next';",
          "import { useTranslation } from 'react-i18next';\nimport { ToolComponentProps } from '@tools/defineTool';"
        );
      }
      modified = true;
    }

    // Remove references to undefined longDescription
    if (content.includes('longDescription') && !content.includes('const longDescription')) {
      content = content.replace(/description: longDescription/g, "description: 'Tool description'");
      content = content.replace(/\$\{longDescription\}/g, 'Tool description');
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.fixedFiles++;
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`⏭️  No fixes needed: ${filePath}`);
    }
  }

  fixAllFiles() {
    console.log('🔧 Starting component fixes...\n');
    
    const files = glob.sync('src/pages/tools/**/index.tsx', {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    console.log(`Found ${files.length} files to check\n`);
    
    files.forEach(file => {
      this.fixFile(file);
    });
    
    console.log('\n📊 SUMMARY:');
    console.log(`Files fixed: ${this.fixedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const fixer = new ComponentFixer();
  fixer.fixAllFiles();
  console.log('\n✅ Component fixes completed!');
}

module.exports = ComponentFixer;
