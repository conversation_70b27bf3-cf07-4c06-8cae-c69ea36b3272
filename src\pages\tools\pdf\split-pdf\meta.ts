import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('pdf', {
  name: 'Split PDF',
  path: 'split-pdf',
  icon: 'mdi:tools',
  description: 'Extract specific pages from a PDF file using page numbers or ranges (e.g., 1,5-8)',
  shortDescription: 'Extract specific pages from a PDF file',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
