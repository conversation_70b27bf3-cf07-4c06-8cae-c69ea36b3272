import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Tool',
  path: 'transpose-csv',
  icon: 'mdi:tools',
  description: 'Just upload your CSV file in the form below, and this tool will automatically transpose your CSV. In the tool options, you can specify the character that starts the comment lines in the CSV to remove them. Additionally, if the CSV is incomplete (missing values), you can replace missing values with the empty character or a custom character.',
  shortDescription: 'Quickly transpose a CSV file.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
