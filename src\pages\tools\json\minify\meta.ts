import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Tool',
  path: 'minify',
  icon: 'mdi:tools',
  description: 'Minify your JSON by removing all unnecessary whitespace and formatting. This tool compresses JSON data to its smallest possible size while maintaining valid JSON structure.',
  shortDescription: 'Quickly compress JSON file.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
