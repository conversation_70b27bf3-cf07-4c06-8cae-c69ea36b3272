import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Change Colors',
  path: 'change-colors',
  icon: 'mdi:tools',
  description: 'Change Colors tool for image operations. Perform change colors operations efficiently.',
  shortDescription: 'Change Colors tool for image operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
