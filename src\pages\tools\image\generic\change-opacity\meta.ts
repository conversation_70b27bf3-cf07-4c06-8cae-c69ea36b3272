import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Change Opacity',
  path: 'change-opacity',
  icon: 'mdi:tools',
  description: 'Change Opacity tool for image operations. Perform change opacity operations efficiently.',
  shortDescription: 'Change Opacity tool for image operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
