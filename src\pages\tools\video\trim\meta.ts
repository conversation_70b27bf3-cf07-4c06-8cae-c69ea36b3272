import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Trim Video',
  path: 'trim',
  icon: 'mdi:tools',
  description: 'Trim Video tool for video operations. Perform trim operations efficiently.',
  shortDescription: 'Trim Video tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
