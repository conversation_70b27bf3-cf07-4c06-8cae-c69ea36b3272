#!/usr/bin/env node

/**
 * Localization Checker for OmniTools
 * 
 * This script checks for:
 * 1. Hardcoded English strings in React components
 * 2. Missing translations in locale files
 * 3. Components not using useTranslation hook
 * 4. Unused translation keys
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const CONFIG = {
  srcDir: 'src',
  localesDir: 'src/locales',
  supportedLanguages: ['en', 'ru'],
  excludePatterns: [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/*.test.*',
    '**/*.spec.*'
  ],
  // Common English words that might be hardcoded
  suspiciousPatterns: [
    /['"`]Loading['"`]/g,
    /['"`]Search['"`]/g,
    /['"`]Options['"`]/g,
    /['"`]Examples['"`]/g,
    /['"`]Result['"`]/g,
    /['"`]Input['"`]/g,
    /['"`]Output['"`]/g,
    /['"`]Error['"`]/g,
    /['"`]Success['"`]/g,
    /['"`]Warning['"`]/g,
    /['"`]Cancel['"`]/g,
    /['"`]Save['"`]/g,
    /['"`]Delete['"`]/g,
    /['"`]Edit['"`]/g,
    /['"`]Copy['"`]/g,
    /['"`]Paste['"`]/g,
    /['"`]Clear['"`]/g,
    /title:\s*['"`][A-Z][^'"`]*['"`]/g,
    /placeholder:\s*['"`][A-Z][^'"`]*['"`]/g,
    /description:\s*['"`][A-Z][^'"`]*['"`]/g
  ]
};

class LocalizationChecker {
  constructor() {
    this.issues = [];
    this.translations = {};
    this.usedKeys = new Set();
    this.loadTranslations();
  }

  loadTranslations() {
    CONFIG.supportedLanguages.forEach(lang => {
      this.translations[lang] = {};
      const langDir = path.join(CONFIG.localesDir, lang);
      
      if (fs.existsSync(langDir)) {
        const files = fs.readdirSync(langDir).filter(f => f.endsWith('.json'));
        files.forEach(file => {
          const filePath = path.join(langDir, file);
          const namespace = path.basename(file, '.json');
          try {
            this.translations[lang][namespace] = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          } catch (error) {
            this.addIssue('error', `Failed to parse ${filePath}: ${error.message}`);
          }
        });
      }
    });
  }

  addIssue(type, message, file = null, line = null) {
    this.issues.push({ type, message, file, line });
  }

  checkFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // Check if file uses useTranslation
    const usesTranslation = content.includes('useTranslation');
    
    // Check for suspicious hardcoded strings
    const suspiciousStrings = [];
    CONFIG.suspiciousPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        suspiciousStrings.push(...matches);
      }
    });

    // If file has suspicious strings but doesn't use translation
    if (suspiciousStrings.length > 0 && !usesTranslation) {
      this.addIssue(
        'warning',
        `File contains hardcoded strings but doesn't use useTranslation: ${suspiciousStrings.join(', ')}`,
        relativePath
      );
    }

    // Check for translation key usage
    const translationKeyPattern = /t\(['"`]([^'"`]+)['"`]\)/g;
    let match;
    while ((match = translationKeyPattern.exec(content)) !== null) {
      this.usedKeys.add(match[1]);
    }

    return {
      usesTranslation,
      suspiciousStrings: suspiciousStrings.length,
      file: relativePath
    };
  }

  checkAllFiles() {
    console.log('🔍 Checking localization...\n');
    
    const pattern = path.join(CONFIG.srcDir, '**/*.{ts,tsx,js,jsx}');
    const files = glob.sync(pattern, { 
      ignore: CONFIG.excludePatterns 
    });

    const stats = {
      total: files.length,
      withTranslation: 0,
      withSuspiciousStrings: 0,
      clean: 0
    };

    files.forEach(file => {
      const result = this.checkFile(file);
      
      if (result.usesTranslation) {
        stats.withTranslation++;
      }
      
      if (result.suspiciousStrings > 0) {
        stats.withSuspiciousStrings++;
      } else if (result.usesTranslation) {
        stats.clean++;
      }
    });

    return stats;
  }

  checkMissingTranslations() {
    console.log('🔍 Checking for missing translations...\n');
    
    const baseKeys = this.flattenKeys(this.translations['en'] || {});
    
    CONFIG.supportedLanguages.forEach(lang => {
      if (lang === 'en') return;
      
      const langKeys = this.flattenKeys(this.translations[lang] || {});
      const missingKeys = baseKeys.filter(key => !langKeys.includes(key));
      
      if (missingKeys.length > 0) {
        this.addIssue(
          'error',
          `Missing translations in ${lang}: ${missingKeys.slice(0, 10).join(', ')}${missingKeys.length > 10 ? ` and ${missingKeys.length - 10} more` : ''}`
        );
      }
    });
  }

  flattenKeys(obj, prefix = '') {
    let keys = [];
    for (const key in obj) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        keys = keys.concat(this.flattenKeys(obj[key], fullKey));
      } else {
        keys.push(fullKey);
      }
    }
    return keys;
  }

  generateReport() {
    console.log('📊 LOCALIZATION REPORT\n');
    console.log('=' .repeat(50));
    
    const stats = this.checkAllFiles();
    this.checkMissingTranslations();
    
    // Statistics
    console.log('\n📈 STATISTICS:');
    console.log(`Total files checked: ${stats.total}`);
    console.log(`Files using translation: ${stats.withTranslation} (${Math.round(stats.withTranslation/stats.total*100)}%)`);
    console.log(`Files with suspicious strings: ${stats.withSuspiciousStrings}`);
    console.log(`Clean localized files: ${stats.clean}`);
    
    // Issues
    if (this.issues.length > 0) {
      console.log('\n⚠️  ISSUES FOUND:');
      this.issues.forEach((issue, index) => {
        const prefix = issue.type === 'error' ? '❌' : '⚠️ ';
        console.log(`${prefix} ${issue.message}`);
        if (issue.file) {
          console.log(`   📁 ${issue.file}`);
        }
        if (index < this.issues.length - 1) console.log('');
      });
    } else {
      console.log('\n✅ No issues found!');
    }
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (stats.withSuspiciousStrings > 0) {
      console.log('• Add useTranslation hook to files with hardcoded strings');
      console.log('• Replace hardcoded strings with translation keys');
    }
    if (this.issues.some(i => i.type === 'error')) {
      console.log('• Add missing translations to locale files');
    }
    console.log('• Consider using automated tools for translation management');
    
    console.log('\n' + '=' .repeat(50));
    
    return {
      stats,
      issues: this.issues,
      success: this.issues.filter(i => i.type === 'error').length === 0
    };
  }
}

// Run the checker
if (require.main === module) {
  const checker = new LocalizationChecker();
  const report = checker.generateReport();
  
  process.exit(report.success ? 0 : 1);
}

module.exports = LocalizationChecker;
