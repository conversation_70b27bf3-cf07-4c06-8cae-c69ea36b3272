import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Tool',
  path: 'swap-csv-columns',
  icon: 'mdi:tools',
  description: 'Just upload your CSV file in the form below, specify the columns to swap, and the tool will automatically change the positions of the specified columns in the output file. In the tool options, you can specify the column positions or names that you want to swap, as well as fix incomplete data and optionally remove empty records and records that have been commented out.',
  shortDescription: 'Reorder CSV columns.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
