import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('csv', {
  name: 'Swap Csv Columns',
  path: 'swap-csv-columns',
  icon: 'mdi:tools',
  description: 'Swap Csv Columns tool for csv operations. Perform swap csv columns operations efficiently.',
  shortDescription: 'Swap Csv Columns tool for csv operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
