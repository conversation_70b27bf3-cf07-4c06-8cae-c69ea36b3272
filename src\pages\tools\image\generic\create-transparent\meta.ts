import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Create Transparent',
  path: 'create-transparent',
  icon: 'mdi:tools',
  description: 'Create Transparent tool for image operations. Perform create transparent operations efficiently.',
  shortDescription: 'Create Transparent tool for image operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
