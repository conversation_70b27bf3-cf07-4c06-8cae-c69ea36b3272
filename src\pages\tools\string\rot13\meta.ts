import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'ROT13 Encode/Decode',
  path: 'rot13',
  icon: 'mdi:tools',
  description: 'ROT13 Encode/Decode tool for string operations. Perform rot13 operations efficiently.',
  shortDescription: 'ROT13 Encode/Decode tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
