import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'ROT13 Encode/Decode',
  path: 'rot13',
  icon: 'mdi:tools',
  description: 'A simple tool to encode or decode text using the ROT13 cipher, which replaces each letter with the letter 13 positions after it in the alphabet.',
  shortDescription: 'Encode or decode text using ROT13 cipher.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
