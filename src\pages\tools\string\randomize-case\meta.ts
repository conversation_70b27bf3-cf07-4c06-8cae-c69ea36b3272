import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Randomize Case',
  path: 'randomize-case',
  icon: 'mdi:tools',
  description: 'Randomize Case tool for string operations. Perform randomize case operations efficiently.',
  shortDescription: 'Randomize Case tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
