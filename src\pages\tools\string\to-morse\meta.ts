import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'String To Morse',
  path: 'to-morse',
  icon: 'mdi:tools',
  description: 'String To Morse tool for string operations. Perform to morse operations efficiently.',
  shortDescription: 'String To Morse tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
