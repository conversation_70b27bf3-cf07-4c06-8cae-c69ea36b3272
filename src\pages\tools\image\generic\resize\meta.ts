import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('image-generic', {
  name: 'Tool',
  path: 'resize',
  icon: 'mdi:tools',
  description: 'Resize JPG, PNG, SVG or GIF images by pixels or percentage while maintaining aspect ratio or not.',
  shortDescription: 'Resize images easily.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
