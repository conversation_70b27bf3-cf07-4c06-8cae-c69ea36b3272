#!/usr/bin/env node

/**
 * Data Files Localization Script
 * 
 * This script localizes data files with table headers by:
 * 1. Adding useTranslation import where needed
 * 2. Converting hardcoded titles to translation keys
 * 3. Adding corresponding translations to locale files
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class DataLocalizer {
  constructor() {
    this.processedFiles = 0;
    this.modifiedFiles = 0;
    this.translations = {
      en: { data: {} },
      ru: { data: {} }
    };
    
    this.loadExistingTranslations();
  }

  loadExistingTranslations() {
    try {
      const enCommon = JSON.parse(fs.readFileSync('src/locales/en/common.json', 'utf8'));
      const ruCommon = JSON.parse(fs.readFileSync('src/locales/ru/common.json', 'utf8'));
      
      if (enCommon.data) this.translations.en.data = enCommon.data;
      if (ruCommon.data) this.translations.ru.data = ruCommon.data;
    } catch (error) {
      console.log('⚠️  Could not load existing translations');
    }
  }

  translateToRussian(text) {
    const translations = {
      'Material Electrical Properties': 'Электрические свойства материалов',
      'American Wire Gauge': 'Американский калибр проводов',
      'Resistivity at 20°C': 'Удельное сопротивление при 20°C',
      'Diameter': 'Диаметр',
      'Area': 'Площадь',
      'Voltage': 'Напряжение',
      'Current': 'Ток',
      'Resistance': 'Сопротивление',
      'Length': 'Длина',
      'Weight': 'Вес',
      'Sag/Deflection': 'Провисание/Прогиб',
      'Tension': 'Натяжение',
      'Radius': 'Радиус',
      'Volume': 'Объем',
      'Material': 'Материал',
      'Wire Gauge': 'Калибр провода',
      'Total Resistance': 'Общее сопротивление',
      'Total Power Dissipated': 'Общая рассеиваемая мощность',
      'Wire Area': 'Площадь провода',
      'Resistivity': 'Удельное сопротивление',
      'Voltage Drop': 'Падение напряжения'
    };
    
    return translations[text] || text;
  }

  processDataFile(filePath) {
    console.log(`Processing data file: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    const fileName = path.basename(filePath, '.ts');
    
    // Extract titles from the file
    const titleMatches = content.match(/title:\s*['"`]([^'"`]+)['"`]/g);
    
    if (titleMatches) {
      titleMatches.forEach(match => {
        const titleMatch = match.match(/title:\s*['"`]([^'"`]+)['"`]/);
        if (titleMatch) {
          const title = titleMatch[1];
          const key = this.generateKey(fileName, title);
          
          // Store translations
          this.translations.en.data[key] = title;
          this.translations.ru.data[key] = this.translateToRussian(title);
          
          // Replace in content
          content = content.replace(
            match,
            `title: t('common.data.${key}')`
          );
          modified = true;
        }
      });
    }
    
    // Add useTranslation import if needed
    if (modified && !content.includes('useTranslation')) {
      // Check if it's a simple export default object
      if (content.startsWith('export default {')) {
        // Convert to function that returns the object
        content = content.replace(
          'export default {',
          "import { useTranslation } from 'react-i18next';\n\nexport default function() {\n  const { t } = useTranslation();\n  return {"
        );
        content = content.replace(/};$/, '  };\n}');
        modified = true;
      } else if (content.includes('const data: DataTable = {')) {
        // Add import at the top
        content = "import { useTranslation } from 'react-i18next';\n" + content;
        
        // Convert to function
        content = content.replace(
          'const data: DataTable = {',
          'function getData(): DataTable {\n  const { t } = useTranslation();\n  return {'
        );
        content = content.replace(
          'export default data;',
          '}\n\nexport default getData();'
        );
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    } else {
      console.log(`⏭️  Skipped: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  generateKey(fileName, title) {
    // Generate a key based on filename and title
    const cleanTitle = title.toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
    
    return `${fileName}_${cleanTitle}`;
  }

  processGenericCalcFiles() {
    console.log('Processing generic-calc data files...');
    
    const files = glob.sync('src/pages/tools/number/generic-calc/data/*.ts', {
      ignore: ['**/index.ts', '**/types.ts']
    });
    
    files.forEach(file => {
      this.processDataFile(file);
    });
  }

  updateTranslationFiles() {
    console.log('\n📝 Updating translation files...');
    
    try {
      // Load existing common.json files
      const enCommon = JSON.parse(fs.readFileSync('src/locales/en/common.json', 'utf8'));
      const ruCommon = JSON.parse(fs.readFileSync('src/locales/ru/common.json', 'utf8'));
      
      // Add data translations
      enCommon.data = { ...enCommon.data, ...this.translations.en.data };
      ruCommon.data = { ...ruCommon.data, ...this.translations.ru.data };
      
      // Write back
      fs.writeFileSync('src/locales/en/common.json', JSON.stringify(enCommon, null, 2), 'utf8');
      fs.writeFileSync('src/locales/ru/common.json', JSON.stringify(ruCommon, null, 2), 'utf8');
      
      console.log('✅ Updated translation files');
    } catch (error) {
      console.log('⚠️  Could not update translation files:', error.message);
    }
  }

  processAllDataFiles() {
    console.log('🔍 Starting data files localization...\n');
    
    // Process main data files
    const mainDataFiles = [
      'src/datatables/data/material_electrical_properties.ts',
      'src/datatables/data/wire_gauge.ts'
    ];
    
    mainDataFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.processDataFile(file);
      }
    });
    
    // Process generic-calc data files
    this.processGenericCalcFiles();
    
    // Update translation files
    if (this.modifiedFiles > 0) {
      this.updateTranslationFiles();
    }
    
    console.log('\n📊 SUMMARY:');
    console.log(`Total files processed: ${this.processedFiles}`);
    console.log(`Files modified: ${this.modifiedFiles}`);
    console.log(`Files skipped: ${this.processedFiles - this.modifiedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const localizer = new DataLocalizer();
  localizer.processAllDataFiles();
  console.log('\n✅ Data files localization completed!');
}

module.exports = DataLocalizer;
