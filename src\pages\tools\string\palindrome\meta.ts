import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Check Palindrome',
  path: 'palindrome',
  icon: 'mdi:tools',
  description: 'Check Palindrome tool for string operations. Perform palindrome operations efficiently.',
  shortDescription: 'Check Palindrome tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
