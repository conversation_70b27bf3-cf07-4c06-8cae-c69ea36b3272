#!/usr/bin/env node

/**
 * Localize Example Cards Script
 * 
 * This script finds and localizes all exampleCards in tool components by:
 * 1. Finding all files with exampleCards
 * 2. Adding useTranslation hook if not present
 * 3. Replacing hardcoded title and description with translation keys
 * 4. Creating translation entries for all examples
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class ExampleCardsLocalizer {
  constructor() {
    this.processedFiles = 0;
    this.modifiedFiles = 0;
    this.translations = {
      en: {},
      ru: {}
    };
    
    this.loadExistingTranslations();
  }

  loadExistingTranslations() {
    try {
      const enTools = JSON.parse(fs.readFileSync('src/locales/en/tools.json', 'utf8'));
      const ruTools = JSON.parse(fs.readFileSync('src/locales/ru/tools.json', 'utf8'));
      
      this.translations.en = enTools;
      this.translations.ru = ruTools;
    } catch (error) {
      console.log('⚠️  Could not load existing translations');
    }
  }

  getToolPath(filePath) {
    const match = filePath.match(/src[\/\\]pages[\/\\]tools[\/\\](.+)[\/\\]index\.tsx$/);
    return match ? match[1].replace(/\\/g, '/') : null;
  }

  getTranslationKey(toolPath) {
    return toolPath.replace(/\//g, '.');
  }

  setNestedProperty(obj, path, value) {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  translateToRussian(text) {
    // Basic translation mapping
    const translations = {
      'Basic': 'Базовый',
      'Simple': 'Простой',
      'Example': 'Пример',
      'Convert': 'Преобразовать',
      'Check': 'Проверить',
      'Remove': 'Удалить',
      'Add': 'Добавить',
      'Extract': 'Извлечь',
      'Generate': 'Генерировать',
      'Calculate': 'Вычислить',
      'Format': 'Форматировать',
      'Validate': 'Проверить',
      'Transform': 'Преобразовать',
      'Minify': 'Минифицировать',
      'Beautify': 'Форматировать',
      'Escape': 'Экранировать',
      'Duplicate': 'Дублировать',
      'Reverse': 'Обратить',
      'Randomize': 'Рандомизировать',
      'Text': 'Текст',
      'JSON': 'JSON',
      'XML': 'XML',
      'CSV': 'CSV',
      'YAML': 'YAML',
      'Object': 'Объект',
      'Array': 'Массив',
      'List': 'Список',
      'String': 'Строка',
      'Number': 'Число',
      'Time': 'Время',
      'Date': 'Дата',
      'File': 'Файл',
      'Image': 'Изображение',
      'Video': 'Видео',
      'PDF': 'PDF',
      'This example': 'Этот пример',
      'In this example': 'В этом примере',
      'shows': 'показывает',
      'demonstrates': 'демонстрирует',
      'how to': 'как',
      'with': 'с',
      'and': 'и',
      'or': 'или',
      'the': '',
      'a': '',
      'an': ''
    };
    
    let translated = text;
    for (const [en, ru] of Object.entries(translations)) {
      if (ru) {
        translated = translated.replace(new RegExp(`\\b${en}\\b`, 'gi'), ru);
      } else {
        translated = translated.replace(new RegExp(`\\b${en}\\b`, 'gi'), '');
      }
    }
    
    // Clean up extra spaces
    translated = translated.replace(/\s+/g, ' ').trim();
    
    return translated;
  }

  processExampleCardsFile(filePath) {
    console.log(`Processing example cards: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    const toolPath = this.getToolPath(filePath);
    if (!toolPath) {
      console.log(`⚠️  Could not determine tool path for ${filePath}`);
      return;
    }
    
    const translationKey = this.getTranslationKey(toolPath);
    
    // Check if file has exampleCards
    if (!content.includes('exampleCards')) {
      console.log(`⏭️  No exampleCards found: ${filePath}`);
      return;
    }
    
    // Check if already localized
    if (content.includes('t(\'tools:') || content.includes('t("tools:')) {
      console.log(`⏭️  Already localized: ${filePath}`);
      return;
    }
    
    // Add useTranslation import if not present
    if (!content.includes('useTranslation')) {
      const importMatch = content.match(/import.*from\s+['"]react-i18next['"];?/);
      if (!importMatch) {
        // Add import after other imports
        const lastImportMatch = content.match(/import.*from.*['"];?\n/g);
        if (lastImportMatch) {
          const lastImport = lastImportMatch[lastImportMatch.length - 1];
          const insertIndex = content.indexOf(lastImport) + lastImport.length;
          content = content.slice(0, insertIndex) + 
                   "import { useTranslation } from 'react-i18next';\n" + 
                   content.slice(insertIndex);
          modified = true;
        }
      }
    }
    
    // Extract exampleCards
    const exampleCardsMatch = content.match(/const exampleCards[^=]*=\s*\[([^;]+)\];/s);
    if (!exampleCardsMatch) {
      console.log(`⚠️  Could not parse exampleCards in ${filePath}`);
      return;
    }
    
    // Parse individual examples
    const examplesText = exampleCardsMatch[1];
    const examples = this.parseExamples(examplesText);
    
    if (examples.length === 0) {
      console.log(`⚠️  No examples found in ${filePath}`);
      return;
    }
    
    // Store translations
    examples.forEach((example, index) => {
      const exampleKey = `${translationKey}.example${index + 1}`;
      
      this.setNestedProperty(this.translations.en, `${exampleKey}.title`, example.title);
      this.setNestedProperty(this.translations.en, `${exampleKey}.description`, example.description);
      
      this.setNestedProperty(this.translations.ru, `${exampleKey}.title`, this.translateToRussian(example.title));
      this.setNestedProperty(this.translations.ru, `${exampleKey}.description`, this.translateToRussian(example.description));
    });
    
    // Replace exampleCards with localized version
    const localizedExampleCards = this.generateLocalizedExampleCards(examples, translationKey);
    content = content.replace(
      /const exampleCards[^=]*=\s*\[[^;]+\];/s,
      localizedExampleCards
    );
    modified = true;
    
    // Add useTranslation hook in component
    if (!content.includes('const { t } = useTranslation()')) {
      // Find the component function
      const componentMatch = content.match(/export default function \w+\([^)]*\)\s*{/);
      if (componentMatch) {
        const insertIndex = content.indexOf(componentMatch[0]) + componentMatch[0].length;
        content = content.slice(0, insertIndex) + 
                 '\n  const { t } = useTranslation();' + 
                 content.slice(insertIndex);
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      this.modifiedFiles++;
      console.log(`✅ Modified: ${filePath}`);
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
    }
    
    this.processedFiles++;
  }

  parseExamples(examplesText) {
    const examples = [];
    
    // Simple regex to extract title and description
    const titleMatches = examplesText.match(/title:\s*['"`]([^'"`]+)['"`]/g);
    const descriptionMatches = examplesText.match(/description:\s*['"`]([^'"`]+)['"`]/gs);
    
    if (titleMatches && descriptionMatches) {
      const minLength = Math.min(titleMatches.length, descriptionMatches.length);
      
      for (let i = 0; i < minLength; i++) {
        const title = titleMatches[i].match(/title:\s*['"`]([^'"`]+)['"`]/)[1];
        const description = descriptionMatches[i].match(/description:\s*['"`]([^'"`]+)['"`]/s)[1];
        
        examples.push({ title, description });
      }
    }
    
    return examples;
  }

  generateLocalizedExampleCards(examples, translationKey) {
    const exampleItems = examples.map((_, index) => {
      return `  {
    title: t('tools:${translationKey}.example${index + 1}.title'),
    description: t('tools:${translationKey}.example${index + 1}.description'),
    // ... other properties preserved
  }`;
    }).join(',\n');
    
    return `const exampleCards: CardExampleType<any>[] = [
${exampleItems}
];`;
  }

  updateTranslationFiles() {
    console.log('\n📝 Updating translation files...');
    
    try {
      fs.writeFileSync('src/locales/en/tools.json', JSON.stringify(this.translations.en, null, 2), 'utf8');
      fs.writeFileSync('src/locales/ru/tools.json', JSON.stringify(this.translations.ru, null, 2), 'utf8');
      
      console.log('✅ Updated translation files');
    } catch (error) {
      console.log('⚠️  Could not update translation files:', error.message);
    }
  }

  processAllExampleCards() {
    console.log('🔍 Starting example cards localization...\n');
    
    const files = glob.sync('src/pages/tools/**/index.tsx', {
      ignore: ['**/node_modules/**', '**/dist/**', '**/build/**']
    });
    
    // Filter files that contain exampleCards
    const filesWithExamples = files.filter(file => {
      const content = fs.readFileSync(file, 'utf8');
      return content.includes('exampleCards');
    });
    
    console.log(`Found ${filesWithExamples.length} files with exampleCards\n`);
    
    filesWithExamples.forEach(file => {
      this.processExampleCardsFile(file);
    });
    
    // Update translation files
    if (this.modifiedFiles > 0) {
      this.updateTranslationFiles();
    }
    
    console.log('\n📊 SUMMARY:');
    console.log(`Total files processed: ${this.processedFiles}`);
    console.log(`Files modified: ${this.modifiedFiles}`);
    console.log(`Files skipped: ${this.processedFiles - this.modifiedFiles}`);
  }
}

// Main execution
if (require.main === module) {
  const localizer = new ExampleCardsLocalizer();
  localizer.processAllExampleCards();
  console.log('\n✅ Example cards localization completed!');
}

module.exports = ExampleCardsLocalizer;
