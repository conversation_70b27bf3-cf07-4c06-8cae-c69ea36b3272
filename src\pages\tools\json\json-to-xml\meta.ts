import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('json', {
  name: 'Json To Xml',
  path: 'json-to-xml',
  icon: 'mdi:tools',
  description: 'Convert JSON data to XML format with customizable options.',
  shortDescription: 'Convert JSON data to XML format with customizable options.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
