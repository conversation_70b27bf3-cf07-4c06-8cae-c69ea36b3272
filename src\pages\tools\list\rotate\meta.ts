import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Rotate List',
  path: 'rotate',
  icon: 'mdi:tools',
  description: 'A tool to rotate items in a list by a specified number of positions. Shift elements left or right while maintaining their relative order.',
  shortDescription: 'Shift list items by position.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});
