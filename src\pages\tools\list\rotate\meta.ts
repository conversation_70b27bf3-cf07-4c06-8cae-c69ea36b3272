import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Rotate List',
  path: 'rotate',
  icon: 'mdi:tools',
  description: 'Rotate List tool for list operations. Perform rotate operations efficiently.',
  shortDescription: 'Rotate List tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
