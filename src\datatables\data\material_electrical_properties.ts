import { useTranslation } from 'react-i18next';

// Static version for use in non-React contexts
const staticData = {
  title: 'Material Electrical Properties',
  columns: {
    resistivity_20c: {
      title: 'Resistivity at 20°C',
      type: 'number',
      unit: 'Ω/m'
    }
  },
  data: {
    Copper: {
      resistivity_20c: 1.68e-8
    },
    Aluminum: {
      resistivity_20c: 2.82e-8
    }
  }
};

export default function() {
  const { t } = useTranslation();
  return {
    title: t('common.data.materialElectricalProperties'),
    columns: {
      resistivity_20c: {
        title: t('common.data.resistivityAt20C'),
        type: 'number',
        unit: 'Ω/m'
      }
    },
    data: staticData.data
  };
}

// Export static version for non-React usage
export { staticData };
