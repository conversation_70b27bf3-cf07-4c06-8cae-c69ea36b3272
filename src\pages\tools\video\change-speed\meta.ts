import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('video', {
  name: 'Change Speed',
  path: 'change-speed',
  icon: 'mdi:tools',
  description: 'Change Speed tool for video operations. Perform change speed operations efficiently.',
  shortDescription: 'Change Speed tool for video operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
