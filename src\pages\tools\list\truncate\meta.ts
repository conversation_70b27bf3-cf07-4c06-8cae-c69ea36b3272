import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('list', {
  name: 'Truncate',
  path: 'truncate',
  icon: 'mdi:tools',
  description: 'Truncate tool for list operations. Perform truncate operations efficiently.',
  shortDescription: 'Truncate tool for list operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
