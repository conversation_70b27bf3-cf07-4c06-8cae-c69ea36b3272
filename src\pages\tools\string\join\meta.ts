import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Join Text',
  path: 'join',
  icon: 'mdi:tools',
  description: 'Join Text tool for string operations. Perform join operations efficiently.',
  shortDescription: 'Join Text tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
