import { defineTool } from '@tools/defineTool';
import { lazy } from 'react';

export const tool = defineTool('string', {
  name: 'Text Statistics',
  path: 'statistic',
  icon: 'mdi:tools',
  description: 'Text Statistics tool for string operations. Perform statistic operations efficiently.',
  shortDescription: 'Text Statistics tool for string operations.',
  keywords: ["tool"],
  component: lazy(() => import('./index'))
});

export const meta = tool;
